-- Start transaction
BEGIN;

-- Generate comprehensive appointment data for all patients
DO $$
DECLARE
  patient_rec RECORD;
  provider_rec RECORD;
  department_rec UUID;
  appointment_date TIMESTAMP;
  reason_text TEXT;
  status_val appointment_status;
  duration_val INT;
  notes_text TEXT;
  i INTEGER;
  appointment_count INTEGER;
  reasons TEXT[] := ARRAY[
    'Annual physical examination',
    'Follow-up visit',
    'Sick visit - cold/flu symptoms',
    'Vaccination appointment',
    'Specialist consultation',
    'Preventive care screening',
    'Chronic disease management',
    'Prescription refill',
    'Lab results review',
    'Specialist referral',
    'Blood pressure check',
    'Diabetes management',
    'Asthma follow-up',
    'Routine check-up',
    'Medication review',
    'Physical therapy evaluation',
    'Mental health consultation',
    'Dermatology screening',
    'Cardiology consultation',
    'Orthopedic evaluation',
    'Eye examination',
    'Hearing test',
    'Allergy testing',
    'Pre-operative consultation',
    'Post-operative follow-up',
    'Emergency visit',
    'Urgent care visit',
    'Wellness visit',
    'Sports physical',
    'Travel medicine consultation'
  ];
BEGIN
  -- Create appointments for each patient
  FOR patient_rec IN
    SELECT p.id, p.first_name || ' ' || p.last_name AS name, p.organization_id, p.date_of_birth
    FROM public.patients p
  LOOP
    -- Find providers in the same organization
    SELECT COUNT(*) INTO appointment_count
    FROM public.healthcare_providers hp
    WHERE hp.organization_id = patient_rec.organization_id;

    -- Skip if no providers
    CONTINUE WHEN appointment_count = 0;

    -- Find a department in the same organization
    SELECT d.id
    INTO department_rec
    FROM public.departments d
    JOIN public.locations l ON d.location_id = l.id
    WHERE l.organization_id = patient_rec.organization_id
    ORDER BY random()
    LIMIT 1;

    -- Skip if no department
    CONTINUE WHEN department_rec IS NULL;

    -- Create 3-8 appointments per patient (mix of past, present, future)
    FOR i IN 1..(3 + floor(random() * 6)::int) LOOP
      -- Find a random provider in the same organization
      SELECT hp.id, hp.first_name || ' ' || hp.last_name AS name
      INTO provider_rec
      FROM public.healthcare_providers hp
      WHERE hp.organization_id = patient_rec.organization_id
      ORDER BY random()
      LIMIT 1;

      -- Generate appointment date (60 days in past to 90 days in future)
      appointment_date := NOW() + (random() * 150 - 60) * INTERVAL '1 day';

      -- Round to business hours (8 AM to 5 PM, Monday-Friday)
      appointment_date := date_trunc('day', appointment_date) +
                         ((8 + floor(random() * 9)) * INTERVAL '1 hour') +
                         (floor(random() * 4) * INTERVAL '15 minutes');

      -- Select random reason
      reason_text := reasons[1 + (random() * (array_length(reasons, 1) - 1))::int];

      -- Set status based on appointment date
      IF appointment_date < NOW() - INTERVAL '1 day' THEN
        -- Past appointments
        CASE floor(random() * 10)::int
          WHEN 0, 1, 2, 3, 4, 5, 6 THEN status_val := 'completed';
          WHEN 7, 8 THEN status_val := 'cancelled';
          ELSE status_val := 'no_show';
        END CASE;
      ELSIF appointment_date < NOW() + INTERVAL '2 hours' THEN
        -- Current/near appointments
        CASE floor(random() * 6)::int
          WHEN 0, 1 THEN status_val := 'checked_in';
          WHEN 2, 3 THEN status_val := 'in_progress';
          WHEN 4 THEN status_val := 'completed';
          ELSE status_val := 'scheduled';
        END CASE;
      ELSE
        -- Future appointments
        status_val := 'scheduled';
      END IF;

      -- Set duration based on reason and patient age
      CASE
        WHEN reason_text LIKE '%physical%' OR reason_text LIKE '%examination%' THEN
          duration_val := 45 + floor(random() * 15)::int;
        WHEN reason_text LIKE '%consultation%' OR reason_text LIKE '%evaluation%' THEN
          duration_val := 60 + floor(random() * 30)::int;
        WHEN reason_text LIKE '%follow-up%' OR reason_text LIKE '%check%' THEN
          duration_val := 15 + floor(random() * 15)::int;
        WHEN reason_text LIKE '%emergency%' OR reason_text LIKE '%urgent%' THEN
          duration_val := 30 + floor(random() * 60)::int;
        ELSE
          duration_val := 30 + floor(random() * 15)::int;
      END CASE;

      -- Generate comprehensive notes
      notes_text := 'Patient: ' || patient_rec.name ||
                   ' | Provider: ' || provider_rec.name ||
                   ' | Reason: ' || reason_text;

      IF status_val = 'completed' THEN
        notes_text := notes_text || ' | Status: Completed successfully';
      ELSIF status_val = 'cancelled' THEN
        notes_text := notes_text || ' | Status: Cancelled by ' ||
                     CASE WHEN random() < 0.6 THEN 'patient' ELSE 'provider' END;
      ELSIF status_val = 'no_show' THEN
        notes_text := notes_text || ' | Status: Patient did not show';
      END IF;

      -- Insert the appointment
      INSERT INTO public.appointments (
        id,
        organization_id,
        patient_id,
        provider_id,
        department_id,
        appointment_date,
        duration_minutes,
        status,
        reason,
        notes,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_rec.organization_id,
        patient_rec.id,
        provider_rec.id,
        department_rec,
        appointment_date,
        duration_val,
        status_val::appointment_status,
        reason_text,
        notes_text,
        CASE
          WHEN appointment_date < NOW() THEN appointment_date - INTERVAL '1 day'
          ELSE NOW() - (random() * interval '30 days')
        END,
        CASE
          WHEN status_val IN ('completed', 'cancelled', 'no_show') THEN appointment_date + INTERVAL '1 hour'
          ELSE NOW() - (random() * interval '7 days')
        END
      );
    END LOOP;

    -- Create some same-day appointments for variety (10% chance)
    IF random() < 0.1 THEN
      SELECT hp.id, hp.first_name || ' ' || hp.last_name AS name
      INTO provider_rec
      FROM public.healthcare_providers hp
      WHERE hp.organization_id = patient_rec.organization_id
      ORDER BY random()
      LIMIT 1;

      INSERT INTO public.appointments (
        id,
        organization_id,
        patient_id,
        provider_id,
        department_id,
        appointment_date,
        duration_minutes,
        status,
        reason,
        notes,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_rec.organization_id,
        patient_rec.id,
        provider_rec.id,
        department_rec,
        date_trunc('day', NOW()) + ((9 + floor(random() * 7))::integer * INTERVAL '1 hour'),
        30,
        CASE WHEN random() < 0.3 THEN 'scheduled'::appointment_status ELSE 'checked_in'::appointment_status END,
        'Same-day appointment',
        'Same-day appointment for ' || patient_rec.name || ' with ' || provider_rec.name,
        NOW() - INTERVAL '2 hours',
        NOW() - INTERVAL '1 hour'
      );
    END IF;
  END LOOP;
END
$$;

COMMIT;