import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Organization as ContextOrganization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { supabase } from "@/lib/supabase";
import { Building2, Calendar, Eye, Plus, Search, Users } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Enhanced organization interface with stats
interface OrganizationWithStats {
  id: string;
  name: string;
  type: string;
  settings: Record<string, unknown>;
  created_at: string | null;
  updated_at: string | null;
  user_count: number;
  patient_count: number;
  user_role?: string;
  is_current?: boolean;
}

export function ImprovedOrganizationsPage() {
  const { user, organization: currentOrg, setOrganization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();

  const [organizations, setOrganizations] = useState<OrganizationWithStats[]>(
    [],
  );
  const [filteredOrganizations, setFilteredOrganizations] = useState<
    OrganizationWithStats[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch organizations with stats
  const fetchOrganizations = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      if (isSystemAdmin) {
        // System admins see all organizations
        const { data: allOrgs, error: orgsError } = await supabase
          .from("organizations")
          .select("*")
          .order("name");

        if (orgsError) throw orgsError;

        // Get stats for each organization
        const orgsWithStats = await Promise.all(
          (allOrgs || []).map(async (org) => {
            // Get user count
            const { count: userCount } = await supabase
              .from("user_roles")
              .select("*", { count: "exact", head: true })
              .eq("organization_id", org.id);

            // Get patient count (if patients table exists)
            let patientCount = 0;
            try {
              const result = await supabase
                .from("patients")
                .select("*", { count: "exact", head: true })
                .eq("organization_id", org.id);
              patientCount = result.count || 0;
            } catch {
              // Fallback if patients table doesn't exist
              patientCount = 0;
            }

            return {
              ...org,
              settings: (org.settings as Record<string, unknown>) || {},
              user_count: userCount || 0,
              patient_count: patientCount,
              user_role: "system_admin",
              is_current: currentOrg?.id === org.id,
            };
          }),
        );

        setOrganizations(orgsWithStats);
        setFilteredOrganizations(orgsWithStats);
      } else {
        // Regular users see only their organizations
        const { data, error } = await supabase
          .from("user_roles")
          .select(
            `
            role,
            organization:organizations (
              id, name, type, settings, created_at, updated_at
            )
          `,
          )
          .eq("user_id", user.id);

        if (error) throw error;

        const rawData = data as unknown as Array<{
          role: string;
          organization: {
            id: string;
            name: string;
            type: string;
            settings: Record<string, unknown>;
            created_at: string;
            updated_at: string;
          } | null;
        }>;

        // Transform and get stats for user's organizations
        const userOrgsWithStats = await Promise.all(
          rawData
            .filter((item) => item.organization !== null)
            .map(async (item) => {
              const org = item.organization!;

              // Get user count
              const { count: userCount } = await supabase
                .from("user_roles")
                .select("*", { count: "exact", head: true })
                .eq("organization_id", org.id);

              // Get patient count
              let patientCount = 0;
              try {
                const result = await supabase
                  .from("patients")
                  .select("*", { count: "exact", head: true })
                  .eq("organization_id", org.id);
                patientCount = result.count || 0;
              } catch {
                // Fallback if patients table doesn't exist
                patientCount = 0;
              }

              return {
                ...org,
                settings: (org.settings as Record<string, unknown>) || {},
                user_count: userCount || 0,
                patient_count: patientCount,
                user_role: item.role,
                is_current: currentOrg?.id === org.id,
              };
            }),
        );

        setOrganizations(userOrgsWithStats);
        setFilteredOrganizations(userOrgsWithStats);
      }
    } catch (error) {
      console.error("Error fetching organizations:", error);
      toast.error("Failed to load organizations");
    } finally {
      setIsLoading(false);
    }
  }, [user, isSystemAdmin, currentOrg?.id]);

  // Filter organizations based on search
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredOrganizations(organizations);
    } else {
      const filtered = organizations.filter(
        (org) =>
          org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          org.type.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredOrganizations(filtered);
    }
  }, [searchTerm, organizations]);

  useEffect(() => {
    fetchOrganizations();
  }, [fetchOrganizations]);

  // Handle organization selection
  const handleOrganizationSelect = async (org: OrganizationWithStats) => {
    try {
      const fullOrg = {
        id: org.id,
        name: org.name,
        type: org.type,
        settings: org.settings,
        billing_info: {},
        created_at: org.created_at,
        subscription_tier: "free",
        updated_at: org.updated_at,
      };

      setOrganization(fullOrg as unknown as ContextOrganization);
      toast.success(`Switched to ${org.name}`);
      navigate("/dashboard", { replace: true });
    } catch (error) {
      console.error("Error switching organization:", error);
      toast.error("Failed to switch organization");
    }
  };

  // Handle view organization details
  const handleViewOrganization = (orgId: string) => {
    navigate(`/organizations/${orgId}/settings`);
  };

  const handleCreateOrganization = () => {
    navigate("/setup/organization");
  };

  const pageTitle = isSystemAdmin ? "All Organizations" : "Your Organizations";
  const pageDescription = isSystemAdmin
    ? "Manage all organizations in the system"
    : "Choose an organization to work with or create a new one";

  return (
    <div className="container py-8 max-w-7xl">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{pageTitle}</h1>
          <p className="text-muted-foreground mt-1">{pageDescription}</p>
        </div>
        <Button onClick={handleCreateOrganization}>
          <Plus className="mr-2 h-4 w-4" /> Create Organization
        </Button>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search organizations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="flex flex-col items-center gap-2">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="text-sm text-muted-foreground">
              Loading organizations...
            </p>
          </div>
        </div>
      ) : filteredOrganizations.length > 0 ? (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Organization</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Patients</TableHead>
                <TableHead>Your Role</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrganizations.map((org) => (
                <TableRow
                  key={org.id}
                  className={`cursor-pointer hover:bg-muted/50 ${org.is_current ? "bg-primary/5" : ""}`}
                  onClick={() => handleViewOrganization(org.id)}
                >
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Building2 className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          {org.name}
                          {org.is_current && (
                            <Badge variant="secondary" className="text-xs">
                              Current
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{org.type}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      {org.user_count}
                    </div>
                  </TableCell>
                  <TableCell>{org.patient_count}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        org.user_role === "system_admin"
                          ? "default"
                          : "secondary"
                      }
                    >
                      {org.user_role?.replace("_", " ")}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      {org.created_at ? new Date(org.created_at).toLocaleDateString() : 'N/A'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewOrganization(org.id);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {!org.is_current && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOrganizationSelect(org);
                          }}
                        >
                          Select
                        </Button>
                      )}
                      {org.is_current && (
                        <Button
                          variant="default"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate("/dashboard");
                          }}
                        >
                          Dashboard
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      ) : (
        <div className="text-center py-12 border rounded-lg bg-muted/20">
          <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">
            {searchTerm ? "No Organizations Found" : "No Organizations"}
          </h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            {searchTerm
              ? `No organizations found matching "${searchTerm}"`
              : "You don't have any organizations yet. Create your first organization to get started."}
          </p>
          {!searchTerm && (
            <Button onClick={handleCreateOrganization}>
              <Plus className="mr-2 h-4 w-4" /> Create Your First Organization
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
