# Task ID: 7
# Title: Implement Global Activity Feed
# Status: pending
# Dependencies: 3
# Priority: medium
# Description: Create a system-wide activity feed that displays events and alerts across all organizations.
# Details:
1. Create `useGlobalActivity.ts` hook for fetching activity data
2. Implement real-time updates using WebSocket or Server-Sent Events
3. Create `GlobalActivityFeed.tsx` component
4. Use @mantine/core components for feed items and layout
5. Implement infinite scrolling for efficient loading of activities
6. Add filtering options (by organization, activity type, time range)
7. Create priority highlighting for critical issues
8. Implement grouping of similar activities to reduce clutter

# Test Strategy:
1. Unit test activity fetching and filtering logic
2. Test real-time update functionality
3. Verify infinite scrolling behavior
4. Test filter and search functionality
5. Perform stress tests with high-frequency activity updates
