# Task ID: 6
# Title: Develop Organization Performance Grid
# Status: pending
# Dependencies: 4, 5
# Priority: high
# Description: Create a grid layout displaying performance cards for each organization with key metrics and health indicators.
# Details:
1. Create `OrganizationPerformanceGrid.tsx` component
2. Implement grid layout using CSS Grid or @mantine/core components
3. Create `OrganizationCard` subcomponent for individual org display
4. Integrate with `useOrganizationHealth` for health indicators
5. Add quick action buttons (View Details, Manage Organization)
6. Implement virtualization for efficient rendering of large lists:
   ```typescript
   import { VirtualizedList } from 'react-virtualized';
   ```
7. Add search functionality using fuse.js for fuzzy searching
8. Implement filtering options (by health status, size, etc.)

# Test Strategy:
1. Unit test OrganizationCard component
2. Test search and filter functionality
3. Verify correct health indicator display
4. Performance test with a large number of organizations
5. Test responsiveness and layout on various screen sizes
