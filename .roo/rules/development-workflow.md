---
description:
globs:
alwaysApply: false
---

# Development Workflow & Commands

## Getting Started

```bash
# Install dependencies
npm install

# Start development servers (web + mobile + supabase types)
npm run dev

# Start only web application
cd apps/web && npm run dev

# Generate TypeScript types from Supabase schema
npm run generate-types
```

## Code Quality

```bash
# TypeScript type checking
npx tsc --noEmit

# ESLint checking
npm run lint

# Build for production
npm run build
```

## Database Management

```bash
# Start local Supabase
supabase start

# Reset database
supabase db reset

# Apply migrations
supabase db push

# Generate types
supabase gen types typescript --local --schema public > packages/supabase-types/src/generated-types.ts
```

## Project Structure

- `apps/web/` - Main React application
- `apps/mobile/` - React Native mobile app
- `packages/supabase-types/` - Shared TypeScript types
- `supabase/` - Database schema and migrations

## Common Development Tasks

### Adding New Components

1. Create component in appropriate directory under `apps/web/src/components/`
2. Export from `apps/web/src/components/index.ts` if needed
3. Add to page exports in `apps/web/src/pages/index.ts` for pages

### Database Changes

1. Create migration: `supabase migration new migration_name`
2. Write SQL in the generated file
3. Apply: `supabase db push`
4. Regenerate types: `npm run generate-types`

### Fixing TypeScript Errors

1. Run `npx tsc --noEmit` to see all errors
2. Common fixes:
   - Add null checks: `{user && <Component />}`
   - Convert types: `!!value` for boolean conversion
   - Handle promises: `'key' in object` instead of `object[key]`
   - Add ESLint disables for necessary `any` usage

### Performance Optimization

1. Check network tab for multiple queries
2. Combine related data fetches into single queries
3. Use React.memo for expensive components
4. Implement proper loading states

## Debugging Tips

- Use browser dev tools Network tab to inspect Supabase queries
- Check console for RLS policy violations
- Use `console.debug` for development logging (removed in production)
- Test with different user roles (system admin, org admin, regular user)
