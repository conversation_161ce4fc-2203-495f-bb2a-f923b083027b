import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAnalytics } from "@/hooks/dashboard/useAnalytics";
import { useLocationAnalytics } from "@/hooks/dashboard/useLocationAnalytics";
import { useLocations } from "@/hooks/useLocations";
import { BarChart3 } from "lucide-react";

export function AppointmentMetrics() {
  const { selectedLocation, shouldShowLocationSelector } = useLocations();

  // Conditionally use location-aware analytics when location is selected
  const shouldUseLocationAnalytics = shouldShowLocationSelector && 
    selectedLocation && 
    selectedLocation.id !== 'system-admin-all-locations';
  
  const orgAnalytics = useAnalytics();
  const locationAnalytics = useLocationAnalytics();
  
  // Choose which analytics to use based on location selection
  const { appointmentMetrics, isLoading, error } = shouldUseLocationAnalytics 
    ? locationAnalytics 
    : orgAnalytics;

  // Function to render a simple horizontal bar chart
  const renderHorizontalBarChart = (
    data: { label: string; value: number }[],
  ) => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    const maxValue = Math.max(...data.map((item) => item.value));

    // Generate colors for bars
    const getBarColor = (index: number) => {
      const colors = [
        "bg-blue-500",
        "bg-green-500",
        "bg-amber-500",
        "bg-purple-500",
        "bg-indigo-500",
        "bg-red-500",
        "bg-teal-500",
        "bg-pink-500",
      ];
      return colors[index % colors.length];
    };

    return (
      <div className="space-y-3">
        {data.map((item, index) => {
          const percentage =
            total > 0 ? Math.round((item.value / total) * 100) : 0;
          const width = maxValue > 0 ? (item.value / maxValue) * 100 : 0;

          return (
            <div key={index} className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span className="font-medium">{item.label}</span>
                <span>
                  {item.value} ({percentage}%)
                </span>
              </div>
              <div className="h-2 w-full rounded-full bg-muted overflow-hidden">
                <div
                  className={`h-full rounded-full ${getBarColor(index)}`}
                  style={{ width: `${width}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-medium">
            Appointment Metrics
          </CardTitle>
          <CardDescription>
            Distribution by status and department
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <p className="text-sm text-red-500">
              Failed to load appointment metrics
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-medium">
            Appointment Metrics
          </CardTitle>
          <CardDescription>
            Distribution by status and department
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-4 w-32 mb-2" />
            <div className="space-y-3">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-24" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                  <Skeleton className="h-2 w-full" />
                </div>
              ))}
            </div>

            <Skeleton className="h-4 w-32 mt-6 mb-2" />
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-32" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                  <Skeleton className="h-2 w-full" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Check if we have data to display
  const hasStatusData = appointmentMetrics.byStatus.some(
    (item) => item.value > 0,
  );
  const hasDepartmentData = appointmentMetrics.byDepartment.some(
    (item) => item.value > 0,
  );

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle className="text-lg font-medium">
          Appointment Metrics
        </CardTitle>
        <CardDescription>Distribution by status and department</CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        {!hasStatusData && !hasDepartmentData ? (
          <div className="flex flex-col items-center justify-center h-60 gap-4">
            <BarChart3 className="h-16 w-16 text-muted-foreground" />
            <p className="text-muted-foreground text-center">
              Not enough appointment data to display metrics.
              <br />
              Schedule more appointments to see statistics.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {hasStatusData && (
              <div>
                <h4 className="text-sm font-medium mb-3">
                  Appointments by Status
                </h4>
                {renderHorizontalBarChart(appointmentMetrics.byStatus)}
              </div>
            )}

            {hasDepartmentData && (
              <div>
                <h4 className="text-sm font-medium mb-3">Top Departments</h4>
                {renderHorizontalBarChart(appointmentMetrics.byDepartment)}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
