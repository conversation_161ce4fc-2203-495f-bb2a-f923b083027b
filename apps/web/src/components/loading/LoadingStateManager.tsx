import { useAuth } from "@/hooks/useAuth";
import { secureStorage } from "@/lib/secure-storage";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { LoadingScreen } from "./LoadingScreen";

export function LoadingStateManager({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, organization, isLoading } = useAuth();

  // Track if this is a fresh session (just logged in) vs page refresh
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const hasShownLoadingRef = useRef(false);

  // Track if we need to show the recovery button after a timeout
  const [showRecoveryButton, setShowRecoveryButton] = useState(false);

  // Track if we should show the manual recovery UI after a longer timeout
  const [, setShowManualRecoveryUI] = useState(false);

  // Track loading UI state with refs to avoid extra renders
  const recoveryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const manualRecoveryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Count mount/update cycles to detect excessive updates
  const renderCountRef = useRef(0);

  // Track when loading started
  const loadingStartTimeRef = useRef(Date.now());

  // Record the last seen state to detect potential loops
  const lastSeenStateRef = useRef({
    isLoading,
    userId: user?.id,
    hasOrg: !!organization,
  });

  // Detect if user has cached auth data (indicating page refresh vs fresh login)
  useEffect(() => {
    const checkCachedAuth = async () => {
      try {
        // Check if we have cached session data
        const cachedSession = secureStorage.getItem('supabase.auth.token');
        const hasUserInStorage = !!cachedSession;

        // If we have cached auth and a user, this is likely a page refresh
        if (hasUserInStorage && user) {
          setIsInitialLoad(false);
        }

        // After a short delay, mark initial load as complete regardless
        // This prevents infinite loading on edge cases
        setTimeout(() => {
          setIsInitialLoad(false);
        }, 2000);
      } catch (error) {
        console.warn("Error checking cached auth:", error);
        setIsInitialLoad(false);
      }
    };

    checkCachedAuth();
  }, [user]);

  // Clean loading state logic that minimizes re-renders
  const shouldShowLoading = useMemo(() => {
    console.log('[LOADING_STATE_MANAGER] Loading decision:', {
      user: !!user,
      isLoading,
      isInitialLoad,
      decision: 'calculating...'
    });

    // Never show loading if user is already authenticated (page refresh scenario)
    if (user) {
      console.log('[LOADING_STATE_MANAGER] User exists, skipping loading screen');
      return false;
    }

    // Only show loading during true initial app loads
    const shouldShow = isLoading && isInitialLoad;
    console.log('[LOADING_STATE_MANAGER] Final decision:', shouldShow);
    return shouldShow;
  }, [isLoading, isInitialLoad, user]);

  // Detect and log potential state loops
  useEffect(() => {
    renderCountRef.current += 1;

    // Track state changes
    const currentState = {
      isLoading,
      userId: user?.id,
      hasOrg: !!organization,
    };

    // Log excessive renders in development
    if (process.env.NODE_ENV === "development" && renderCountRef.current > 20) {
      console.warn(
        `LoadingStateManager rendered ${renderCountRef.current} times. ` +
          `Current state: ${JSON.stringify(currentState)}`,
      );
    }

    lastSeenStateRef.current = currentState;
  }, [isLoading, user?.id, organization]);

  // Handler for manually reloading the app
  const handleReloadApplication = useCallback(() => {
    if (process.env.NODE_ENV === "development") {
      console.info("Manual reload triggered");
    }

    // Force a hard reload to clear any React state issues
    window.location.reload();
  }, []);

  // Handler for manually going back to login if stuck
  const handleBackToLogin = useCallback(() => {
    // Clear UI state
    setShowRecoveryButton(false);
    setShowManualRecoveryUI(false);

    // Clear any pending timeouts
    if (recoveryTimeoutRef.current) {
      clearTimeout(recoveryTimeoutRef.current);
      recoveryTimeoutRef.current = null;
    }

    if (manualRecoveryTimeoutRef.current) {
      clearTimeout(manualRecoveryTimeoutRef.current);
      manualRecoveryTimeoutRef.current = null;
    }

    // Log the action in development
    if (process.env.NODE_ENV === "development") {
      console.info("Manual recovery triggered - returning to login");
    }

    // Navigate to login by replacing location (avoid React Router for simplicity)
    window.location.href = "/login";

    // Try to clear any cached state that might be causing issues
    try {
      secureStorage.clear();
      sessionStorage.clear();
    } catch (error) {
      console.warn("Error clearing cache during recovery:", error);
    }
  }, []);

  // Set up a recovery button if loading takes too long
  useEffect(() => {
    // Reset loading start time when loading state changes
    if (shouldShowLoading) {
      loadingStartTimeRef.current = Date.now();
    }

    // Only set up recovery timeout during loading
    if (shouldShowLoading) {
      // Show recovery button after 3 seconds of loading
      recoveryTimeoutRef.current = setTimeout(() => {
        setShowRecoveryButton(true);

        if (process.env.NODE_ENV === "development") {
          console.info("Showing recovery button - loading took too long");
        }

        // Show manual recovery UI after 5 more seconds (8 seconds total)
        manualRecoveryTimeoutRef.current = setTimeout(() => {
          setShowManualRecoveryUI(true);

          if (process.env.NODE_ENV === "development") {
            console.info("Showing manual recovery UI - loading appears stuck");
          }
        }, 5000);
      }, 3000);
    } else {
      // Clear recovery UI when not loading
      setShowRecoveryButton(false);
      setShowManualRecoveryUI(false);
    }

    // Cleanup function to clear timeouts on unmount or deps change
    return () => {
      if (recoveryTimeoutRef.current) {
        clearTimeout(recoveryTimeoutRef.current);
        recoveryTimeoutRef.current = null;
      }

      if (manualRecoveryTimeoutRef.current) {
        clearTimeout(manualRecoveryTimeoutRef.current);
        manualRecoveryTimeoutRef.current = null;
      }
    };
  }, [shouldShowLoading]);


  // Render loading screen or children based on loading state
  if (shouldShowLoading) {
    console.log('[LOADING_STATE_MANAGER] 🔄 SHOWING LOADING SCREEN');
    return (
      <LoadingScreen
        isPostLoginLoading={!!user}
        showRecoveryButton={showRecoveryButton}
        handleBackToLogin={handleBackToLogin}
        handleReloadApplication={handleReloadApplication}
      />
    );
  }

  console.log('[LOADING_STATE_MANAGER] ✅ RENDERING CHILDREN (no loading screen)');
  // Not loading, render children
  return <>{children}</>;
}
