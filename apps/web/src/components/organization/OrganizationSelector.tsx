import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useOrganization } from "@/hooks/useOrganization";
import { useUserRoles } from "@/hooks/useUserRoles";
import { Building2, ChevronDown, Edit, Search } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export function OrganizationSelector() {
  const {
    currentOrg,
    availableOrgs,
    isLoading,
    switchOrganization,
    hasMultipleOrgs,
    isSystemAdmin
  } = useOrganization();
  const { isAdmin } = useUserRoles();
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [filteredOrganizations, setFilteredOrganizations] = useState(availableOrgs);
  const [searchTerm, setSearchTerm] = useState("");
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Update filtered organizations when available orgs change
  useEffect(() => {
    setFilteredOrganizations(availableOrgs);
  }, [availableOrgs]);

  // Filter organizations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredOrganizations(availableOrgs);
    } else {
      const filtered = availableOrgs.filter(
        (org) =>
          org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          org.type?.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredOrganizations(filtered);
    }
    setHighlightedIndex(-1);
  }, [searchTerm, availableOrgs]);

  const handleOrganizationSelect = async (orgId: string) => {
    try {
      setIsDropdownOpen(false);
      await switchOrganization(orgId);
      const selectedOrg = availableOrgs.find(org => org.id === orgId);
      toast.success(`Switched to ${selectedOrg?.name}`);
      navigate("/dashboard", { replace: true });
    } catch (err) {
      console.error("Failed to switch organization:", err);
      toast.error("Failed to switch organization");
    }
  };

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/organizations/${orgId}/settings`);
  };

  const canSwitchOrgs = isAdmin && (isSystemAdmin || hasMultipleOrgs);

  if (!currentOrg && availableOrgs.length === 0) {
    return (
      <Button variant="ghost" size="sm" className="flex items-center gap-2" disabled>
        <Building2 className="h-4 w-4" />
        <span className="max-w-[220px] truncate">No Organization</span>
      </Button>
    );
  }

  if (!canSwitchOrgs) {
    return (
      <Button variant="ghost" size="sm" className="flex items-center gap-2" disabled={isLoading}>
        <Building2 className="h-4 w-4" />
        <span className="max-w-[220px] truncate font-medium">
          {currentOrg?.name || "Select Organization"}
        </span>
      </Button>
    );
  }

  return (
    <DropdownMenu
      open={isDropdownOpen}
      onOpenChange={(open) => {
        setIsDropdownOpen(open);
        if (!open) {
          // Delay clearing search term to prevent flash during close animation
          setTimeout(() => {
            setSearchTerm("");
            setHighlightedIndex(-1);
          }, 150);
        } else {
          // Focus the search input when dropdown opens
          setTimeout(() => {
            searchInputRef.current?.focus();
          }, 100);
        }
      }}
    >
      <DropdownMenuTrigger asChild disabled={isLoading}>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 min-w-[280px]"
        >
          <Building2 className="h-5 w-5 text-primary" />
          <span className="max-w-[220px] truncate font-medium">
            {currentOrg?.name || "Select Organization"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50 ml-auto" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="center"
        className="w-[380px]"
      >
        <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Search Input - only show if there are multiple organizations */}
        {availableOrgs.length > 3 && (
          <>
            <div className="px-2 py-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search organizations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 h-8"
                  autoComplete="off"
                />
              </div>
            </div>
            <DropdownMenuSeparator />
          </>
        )}

        {/* Organizations List */}
        <div ref={dropdownRef} className="max-h-[300px] overflow-y-auto">
          {filteredOrganizations.map((org, index) => {
            const isCurrent = currentOrg && currentOrg.id === org.id;
            return (
              <DropdownMenuItem
                key={org.id}
                disabled={isLoading || !!isCurrent}
                className={`flex items-center justify-between ${
                  highlightedIndex === index ? "bg-muted" : ""
                }`}
                onSelect={(e) => {
                  e.preventDefault();
                  handleOrganizationSelect(org.id);
                }}
                onMouseEnter={() => setHighlightedIndex(index)}
                data-index={index}
              >
                <div className="flex items-center flex-1">
                  <span className="truncate">{org.name}</span>
                  {isCurrent && (
                    <span className="ml-2 text-xs text-primary font-medium">
                      (Current)
                    </span>
                  )}
                </div>
                {isSystemAdmin && (
                  <Button
                    data-edit="true"
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100"
                    onMouseDown={(e) => handleEditOrganization(e, org.id)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
              </DropdownMenuItem>
            );
          })}

          {filteredOrganizations.length === 0 && searchTerm && (
            <div className="px-2 py-4 text-center text-sm text-muted-foreground">
              No organizations found matching "{searchTerm}"
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
