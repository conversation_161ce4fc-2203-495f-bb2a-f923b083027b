import { Organization } from "@/contexts/auth-context-types";
import secureStorage from "@/lib/secure-storage";
import { getUserOrganizations, isUserSystemAdmin } from "@/services/organization-service";
import { User } from "@supabase/supabase-js";
import { create } from "zustand";

interface OrganizationState {
  // Current state
  currentOrg: Organization | null;
  availableOrgs: Organization[];
  isSystemAdmin: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setCurrentOrg: (org: Organization) => void;
  loadUserOrganizations: (user: User) => Promise<void>;
  switchOrganization: (orgId: string) => Promise<void>;
  clearCache: () => void;
}

const CACHE_KEY = "user_organizations";
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

export const useOrganizationStore = create<OrganizationState>((set, get) => ({
  currentOrg: null,
  availableOrgs: [],
  isSystemAdmin: false,
  isLoading: false,
  error: null,

  setCurrentOrg: (org: Organization) => {
    set({ currentOrg: org });
    // Cache the selection
    secureStorage.setOrganizationData("current_org", org.id, CACHE_DURATION);
  },

  loadUserOrganizations: async (user: User) => {
    set({ isLoading: true, error: null });

    try {
      // Check cache first
      const cached = secureStorage.getOrganizationData<{
        orgs: Organization[];
        currentOrgId: string;
        isSystemAdmin: boolean;
        timestamp: number;
      }>(CACHE_KEY);

      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        const currentOrg = cached.orgs.find((org: Organization) => org.id === cached.currentOrgId);
        set({
          availableOrgs: cached.orgs,
          currentOrg: currentOrg || cached.orgs[0],
          isSystemAdmin: cached.isSystemAdmin,
          isLoading: false
        });
        return;
      }

      // Fetch from API using the service
      const organizations = await getUserOrganizations(user);
      const systemAdmin = await isUserSystemAdmin(user.id);

      // Get current org from cache or default appropriately
      const cachedCurrentOrgId = secureStorage.getOrganizationData<string>("current_org");
      let currentOrg: Organization | undefined;

      if (cachedCurrentOrgId) {
        currentOrg = organizations.find((org: Organization) => org.id === cachedCurrentOrgId);
      }

      // If no cached org or cached org not found, use appropriate default
      if (!currentOrg) {
        if (systemAdmin) {
          // For system admins, default to "All Organizations" view
          currentOrg = organizations.find((org: Organization) => org.id === "system-admin-all-orgs") || organizations[0];
        } else {
          // For regular users, default to first organization
          currentOrg = organizations[0];
        }
      }

      // Cache the results
      secureStorage.setOrganizationData(CACHE_KEY, {
        orgs: organizations,
        currentOrgId: currentOrg?.id,
        isSystemAdmin: systemAdmin,
        timestamp: Date.now()
      }, CACHE_DURATION);

      set({
        availableOrgs: organizations,
        currentOrg,
        isSystemAdmin: systemAdmin,
        isLoading: false
      });

    } catch (err) {
      console.error("Failed to load organizations:", err);
      set({ isLoading: false, error: "Failed to load organizations" });
    }
  },

  switchOrganization: async (orgId: string) => {
    const { availableOrgs } = get();
    const org = availableOrgs.find(o => o.id === orgId);
    if (org) {
      get().setCurrentOrg(org);
    }
  },

  clearCache: () => {
    secureStorage.remove(CACHE_KEY);
    secureStorage.remove("current_org");
    set({ currentOrg: null, availableOrgs: [], isSystemAdmin: false, error: null });
  }
}));