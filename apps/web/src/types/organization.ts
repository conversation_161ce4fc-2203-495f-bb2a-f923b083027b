import { Organization } from "@/contexts/auth-context-types";

export interface OrganizationWithHierarchy extends Organization {
  parent_id?: string;
  children?: OrganizationWithHierarchy[];
  level?: number;
}

export interface UserOrganizationAccess {
  organizationId: string;
  role: string;
  permissions: string[];
  isActive: boolean;
}

export interface OrganizationCache {
  organizations: Organization[];
  currentOrgId: string;
  timestamp: number;
  userId: string;
} 