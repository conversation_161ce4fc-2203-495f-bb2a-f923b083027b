# Spritely Project Expenses

This document tracks the expenses associated with the Spritely project infrastructure.

## Monthly Recurring Costs

### Supabase

| Environment         | Cost           | Notes                                                    |
| ------------------- | -------------- | -------------------------------------------------------- |
| Production (master) | $25/month      | Base Supabase Pro plan                                   |
| Staging             | ~$10/month     | Preview branch ($0.32/hour = ~$10/month if kept running) |
| Development         | ~$10/month     | Preview branch ($0.32/hour = ~$10/month if kept running) |
| **Total Supabase**  | **~$45/month** |                                                          |

### Netlify

| Environment         | Cost         | Notes                                 |
| ------------------- | ------------ | ------------------------------------- |
| Production (master) | $0/month     | Netlify Starter plan (free)           |
| Staging             | $0/month     | Branch deploy (included in free plan) |
| Development         | $0/month     | Branch deploy (included in free plan) |
| **Total Netlify**   | **$0/month** |                                       |

### 1Password

| Service             | Cost             | Notes                          |
| ------------------- | ---------------- | ------------------------------ |
| Teams               | $19.95/month     | For 5 users ($3.99/user/month) |
| **Total 1Password** | **$19.95/month** |                                |

### GitHub

| Service          | Cost              | Notes |
| ---------------- | ----------------- | ----- |
| Team             | $4/user/month     |       |
| **Total GitHub** | **$4/user/month** |       |

## Total Monthly Expenses

| Service   | Cost                           |
| --------- | ------------------------------ |
| Supabase  | ~$45/month                     |
| Netlify   | $0/month                       |
| 1Password | $19.95/month                   |
| GitHub    | $4/user/month                  |
| **Total** | **~$65/month + $4/user/month** |

## Cost Optimization Strategies

### Supabase

1. **Turn off preview branches when not in use**

   - Preview branches cost $0.32/hour (~$230/month if left running 24/7)
   - Only turn them on during active development and testing
   - Potential savings: Up to $20/month

2. **Use local development**
   - Developers can use local Supabase instances for most development work
   - Only use cloud preview branches for integration testing
   - Potential savings: Reduced usage of preview branches

### Future Considerations

1. **Netlify Pro upgrade**

   - If we need more build minutes or team features: $19/month
   - Only upgrade when necessary

2. **Supabase Enterprise**
   - For HIPAA compliance and additional security features
   - Custom pricing (typically starts at $500/month)
   - Consider when approaching production with real patient data

## One-time Expenses

| Item        | Cost     | Date | Notes |
| ----------- | -------- | ---- | ----- |
| Domain name | $12/year |      |       |

## Notes

- All prices are in USD
- Prices are subject to change based on vendor pricing updates
- Preview branch costs are estimates based on current Supabase pricing
- Consider turning off preview branches when not actively developing to save costs
