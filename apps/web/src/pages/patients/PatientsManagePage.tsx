import { PatientFilters } from "@/components/patients/PatientFilters";
import { PatientStats } from "@/components/patients/PatientStats";
import { PatientTable } from "@/components/patients/PatientTable";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Pagination,
    PaginationContent,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { usePatients } from "@/hooks/patients/usePatients";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { PatientFilters as PatientFiltersType } from "@/types/patient";
import { Plus, Users } from "lucide-react";
import { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

export function PatientsManagePage() {
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [filters, setFilters] = useState<PatientFiltersType>({
    search: "",
    status: "all",
    gender: "all",
  });

  // Get pagination from URL parameters
  const itemsPerPage = 50;
  const pageParam = searchParams.get("page");
  const currentPage = Math.max(1, parseInt(pageParam || "1", 10));
  const offset = (currentPage - 1) * itemsPerPage;

  const { patients, stats, isLoading, totalCount } = usePatients({
    filters,
    limit: itemsPerPage,
    offset,
    includeStats: true,
    includeOrganization:
      isSystemAdmin && organization?.id === "system-admin-all-orgs",
  });

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  // Pagination handlers
  const updatePage = (page: number) => {
    const newParams = new URLSearchParams(searchParams);
    if (page === 1) {
      newParams.delete("page"); // Remove page param for page 1 (cleaner URLs)
    } else {
      newParams.set("page", page.toString());
    }
    setSearchParams(newParams);
  };

  const handlePageChange = (page: number) => {
    updatePage(page);
  };

  const handlePrevPage = () => {
    if (hasPrevPage) {
      updatePage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (hasNextPage) {
      updatePage(currentPage + 1);
    }
  };

  const handleFiltersChange = (newFilters: PatientFiltersType) => {
    setFilters(newFilters);
    updatePage(1); // Reset to first page when filters change
  };

  const handleCreatePatient = () => {
    navigate("/patients/new");
  };











  const getPageTitle = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "All Patients";
    }
    return `${organization?.name || "Organization"} Patients`;
  };

  const getPageDescription = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "Manage patients across all organizations";
    }
    return "Manage and view patient information for your organization";
  };

  return (
    <div className="container py-8 max-w-7xl">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{getPageTitle()}</h1>
          <p className="text-muted-foreground mt-1">{getPageDescription()}</p>
        </div>
        <Button onClick={handleCreatePatient}>
          <Plus className="mr-2 h-4 w-4" />
          Add Patient
        </Button>
      </div>

      {/* Stats */}
      <div className="mb-8">
        <PatientStats stats={stats} isLoading={isLoading} />
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Patient Directory
            </CardTitle>
            <div className="text-sm text-muted-foreground">
              {totalCount} patient{totalCount !== 1 ? "s" : ""}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filters */}
          <PatientFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            totalCount={totalCount}
          />

          {/* Table */}
          <PatientTable patients={patients} isLoading={isLoading} />

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={handlePrevPage}
                      className={!hasPrevPage ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => handlePageChange(pageNum)}
                          isActive={currentPage === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={handleNextPage}
                      className={!hasNextPage ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
