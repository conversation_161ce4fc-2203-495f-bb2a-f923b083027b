import { LoadingScreen } from "@/components/ui/loading-screen";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { useOrganizationStore } from "@/stores/organization-store";
import { useEffect, useMemo, useRef, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";

// Define route types for cleaner navigation logic
type RouteConfig = {
  public: string[];
  protected: string[];
  protectedPatterns: string[]; // Dynamic routes that start with these patterns
  special: {
    root: string;
    defaultAuthenticated: string;
    defaultUnauthenticated: string;
  };
};

type NavigationManagerProps = {
  children: React.ReactNode;
};

export function NavigationManager({ children }: NavigationManagerProps) {
  const { user, organization, isLoading } = useAuth();
  const { isSystemAdmin, isLoading: rolesLoading } = useUserRoles();
  const { isLoading: orgStoreLoading, currentOrg, availableOrgs } = useOrganizationStore();
  const location = useLocation();

  // Track if we've given the org store a chance to load for the current user
  const orgStoreInitializedRef = useRef<string | null>(null);

  // Track organization store initialization
  const orgStoreInitialized = user ? orgStoreInitializedRef.current === user.id : false;

  // Clear org store initialized ref when user changes
  if (!user) {
    orgStoreInitializedRef.current = null;
  }

  // Mark org store as initialized when we have data or when loading completes
  if (user && !orgStoreInitialized && (currentOrg || availableOrgs.length > 0 || (!orgStoreLoading && orgStoreInitializedRef.current !== user.id))) {
    orgStoreInitializedRef.current = user.id;
  }

  // Add minimal logging for debugging (only when there are issues)
  if (!organization && !currentOrg && user && !isLoading && !orgStoreLoading) {
    console.debug(`[NAV_DEBUG] No organization found:`, {
      path: location.pathname,
      user: user.email,
      orgStoreInitialized,
      availableOrgsCount: availableOrgs.length
    });
  }

  // Track redirect attempts to prevent infinite loops
  const redirectCountRef = useRef(0);
  const lastRedirectTimeRef = useRef(0);
  const lastPathRef = useRef("");

  // Track the intended route during loading to preserve it after auth/org loading
  const intendedRouteRef = useRef<string | null>(null);

  // Track loading to prevent infinite loading screens
  const [loadingStartTime] = useState(() => Date.now());
  const [shouldBypassLoading, setShouldBypassLoading] = useState(false);

  // Track render safety to prevent blank screens
  const [safetyRenderEnabled, setSafetyRenderEnabled] = useState(false);

  // Define routes configuration
  const routes: RouteConfig = useMemo(
    () => ({
      public: ["/login", "/register", "/reset-password", "/setup"],
      protected: [
        "/dashboard",
        "/settings",
        "/profile",
        "/organizations",
        "/patients",
      ],
      protectedPatterns: ["/patients/", "/organizations/"], // Dynamic routes that start with these patterns
      special: {
        root: "/",
        defaultAuthenticated: "/dashboard",
        defaultUnauthenticated: "/login",
      },
    }),
    [],
  );

  // Capture intended route when loading starts
  useEffect(() => {
    const currentPath = location.pathname;
    const isProtectedRoute = routes.protected.includes(currentPath) ||
      routes.protectedPatterns.some((pattern) => currentPath.startsWith(pattern));

    // If we're loading and on a protected route, capture it as the intended route
    if ((isLoading || rolesLoading || orgStoreLoading) && isProtectedRoute && user) {
      if (!intendedRouteRef.current) {
        intendedRouteRef.current = currentPath;
        console.debug(`[NAV] Captured intended route during loading: ${currentPath}`);
      }
    }

    // Clear intended route when loading is complete and we have organization
    const effectiveOrganization = organization || currentOrg;
    if (!isLoading && !rolesLoading && !orgStoreLoading && effectiveOrganization && intendedRouteRef.current) {
      console.debug(`[NAV] Clearing intended route, loading complete with org: ${effectiveOrganization.name}`);
      intendedRouteRef.current = null;
    }
  }, [location.pathname, isLoading, rolesLoading, orgStoreLoading, user, organization, currentOrg, routes]);

  // Safety timeout to prevent infinite loading
  useEffect(() => {
    const safetyTimeout = setTimeout(() => {
      setSafetyRenderEnabled(true);
    }, 10000); // 10 seconds safety timeout

    return () => clearTimeout(safetyTimeout);
  }, []);

  // Loading bypass after timeout
  useEffect(() => {
    const loadingTimeout = setTimeout(() => {
      if (isLoading && Date.now() - loadingStartTime > 5000) {
        setShouldBypassLoading(true);
      }
    }, 5000); // 5 seconds timeout

    return () => clearTimeout(loadingTimeout);
  }, [isLoading, loadingStartTime]);

  // Handle navigation logic
  const shouldRedirect = useMemo(() => {
    // Skip during initial loading unless safety timeout reached
    // Include roles loading and org store loading to prevent premature redirects
    const hasUser = !!user;
    const hasOrg = !!organization;
    const hasStoreOrg = !!currentOrg;
    const isStillLoading = isLoading || rolesLoading || orgStoreLoading;

    // If user is authenticated but org store hasn't been initialized yet, wait
    const waitingForOrgStoreInit = hasUser && !orgStoreInitialized;

    // If user is authenticated but no organization in auth context, check if store has org data
    // This handles the race condition where auth loads before org store syncs
    const waitingForOrgSync = hasUser && !hasOrg && hasStoreOrg;

    if ((isStillLoading || waitingForOrgStoreInit || waitingForOrgSync) && !shouldBypassLoading && !safetyRenderEnabled) {
      console.debug(`[NAV] Skipping redirect - loading or waiting: auth=${isLoading}, roles=${rolesLoading}, org=${orgStoreLoading}, waitingInit=${waitingForOrgStoreInit}, waitingSync=${waitingForOrgSync}`);
      return null;
    }

    const currentPath = location.pathname;

    // Don't redirect if we're already on the target path
    if (currentPath === lastPathRef.current) {
      return null;
    }

    // Update last path
    lastPathRef.current = currentPath;

    // Handle root path
    if (currentPath === routes.special.root) {
      // If we have an intended route and user is authenticated with organization, go there
      const effectiveOrganization = organization || currentOrg;
      if (user && effectiveOrganization && intendedRouteRef.current) {
        const intended = intendedRouteRef.current;
        intendedRouteRef.current = null; // Clear it
        console.debug(`[NAV] Redirecting to intended route from root: ${intended}`);
        return intended;
      }
      return user
        ? routes.special.defaultAuthenticated
        : routes.special.defaultUnauthenticated;
    }

    // Public routes are always accessible
    if (routes.public.includes(currentPath)) {
      // But redirect authenticated users away from auth pages
      if (
        user &&
        ["/login", "/register", "/reset-password"].includes(currentPath)
      ) {
        return routes.special.defaultAuthenticated;
      }
      return null;
    }

    // Protected routes require authentication
    const isProtectedRoute =
      routes.protected.includes(currentPath) ||
      routes.protectedPatterns.some((pattern) =>
        currentPath.startsWith(pattern),
      );

    if (isProtectedRoute) {
      if (!user) {
        return routes.special.defaultUnauthenticated;
      }
      // Most protected routes also require an organization, except /organizations itself
      // Check both auth organization and store organization
      const effectiveOrganization = organization || currentOrg;
      if (
        !effectiveOrganization &&
        currentPath !== "/setup" &&
        currentPath !== "/organizations"
      ) {
        // Check if we're still loading organization data to avoid premature redirects
        if (isLoading || rolesLoading || orgStoreLoading) {
          console.debug(`[NAV] Organization required but still loading - staying on ${currentPath}`);
          return null; // Don't redirect while still loading
        }

        console.debug(`[NAV] No organization found, redirecting from ${currentPath}`);
        // System admins should go to organization selection, not setup
        if (isSystemAdmin) {
          return "/organizations";
        }
        return "/setup";
      }

      // If we just finished loading and have an organization, check if we should go to intended route
      if (user && effectiveOrganization && intendedRouteRef.current && currentPath !== intendedRouteRef.current) {
        const intended = intendedRouteRef.current;
        intendedRouteRef.current = null; // Clear it
        console.debug(`[NAV] Redirecting to intended route after org load: ${intended}`);
        return intended;
      }

      return null;
    }

    // Default to dashboard for authenticated users, login for others
    // But if we have an intended route and user is authenticated with organization, go there
    const effectiveOrganization = organization || currentOrg;
    if (user && effectiveOrganization && intendedRouteRef.current) {
      const intended = intendedRouteRef.current;
      intendedRouteRef.current = null; // Clear it
      console.debug(`[NAV] Redirecting to intended route from default: ${intended}`);
      return intended;
    }

    return user
      ? routes.special.defaultAuthenticated
      : routes.special.defaultUnauthenticated;
  }, [
    user,
    organization,
    currentOrg,
    location.pathname,
    isLoading,
    rolesLoading,
    orgStoreLoading,
    orgStoreInitialized,
    shouldBypassLoading,
    safetyRenderEnabled,
    routes,
    isSystemAdmin,
  ]);

  // Handle the actual redirect
  if (shouldRedirect) {
    // Prevent infinite redirects
    const now = Date.now();
    if (now - lastRedirectTimeRef.current < 1000) {
      redirectCountRef.current++;
      if (redirectCountRef.current > 5) {
        console.error("Too many redirects, rendering current page");
        return <>{children}</>;
      }
    } else {
      redirectCountRef.current = 0;
    }
    lastRedirectTimeRef.current = now;

    return <Navigate to={shouldRedirect} replace />;
  }

  // Show loading screen only during initial auth load, not during org store initialization
  // This prevents the jarring flash when refreshing pages
  // Also check if user exists - if they do, don't show loading on page refresh
  if (isLoading && !shouldBypassLoading && !safetyRenderEnabled && !user) {
    return <LoadingScreen />;
  }

  // Render children if no redirect needed
  return <>{children}</>;
}
