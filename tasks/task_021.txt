# Task ID: 21
# Title: Fix Duplicate Auth State Transitions in AuthProvider
# Status: in-progress
# Dependencies: 19, 18, 2
# Priority: high
# Description: Investigate and resolve duplicate authentication state transitions and mount state issues in the AuthProvider component that are causing multiple SIGN_IN and LOAD_ORGANIZATION events and incorrect isMounted.current values. Also address critical loading state issues that are causing the application to get stuck in 'authenticatedNoOrg' state and triggering the recovery UI. CRITICAL UPDATE: The auth initialization isn't starting at all, with no auth state transition logs appearing.
# Details:
## Root Cause Analysis

**CRITICAL DISCOVERY: Auth Initialization Not Starting**
- No auth state transition logs (should see `[AUTH_STATE] 🔄`)
- No organization loading logs (should see `[AUTH] 🔄`)
- Only Vite connection and Supabase initialization logs
- LoadingStateManager timeout triggers (meaning `isLoading` stays true)

**Root Cause Hypotheses:**
1. **JavaScript Error:** There might be a runtime error preventing AuthProvider from initializing
2. **Import Error:** The AuthProvider might not be properly imported/rendered
3. **Supabase Connection Issue:** The auth state change listener might not be setting up
4. **React Error Boundary:** An error might be caught silently

**Issue 1: Multiple loadOrganizationData() calls**
- `loadOrganizationData` is called in both `initializeAuth()` AND `onAuthStateChange()`
- This causes duplicate SIGN_IN → LOAD_ORGANIZATION sequences
- The `loadedOrgForUser.current` guard isn't working properly because it's being reset

**Issue 2: isMounted.current = false during auth loading**
- The `isMounted.current` is set to `false` in the cleanup function
- But the cleanup runs immediately in React Strict Mode
- This causes the component to think it's unmounted during normal operation

**Issue 3: Race condition in auth state machine**
- Multiple events can be dispatched for the same user/session
- No deduplication logic for identical state transitions
- Auth state machine logs every event, even duplicates

**Issue 4: Dependency array causing re-renders**
- `loadOrganizationData` has `[session?.user?.id]` dependency
- This causes the entire useEffect to re-run when session changes
- Leading to multiple auth initialization cycles

**Issue 5: LoadingStateManager timeout issues**
- Auth loading is taking too long, triggering recovery UI
- Possible stuck state in "authenticatedNoOrg" for system admins
- Organization loading might be failing silently
- 15-second timeout might be too aggressive for some scenarios

**Issue 6: CRITICAL - Stuck in 'authenticatedNoOrg' state**
- Application is not transitioning from "authenticatedNoOrg" to "authenticated" state
- Recovery UI appears after timeout (10s for button, 20s for manual recovery)
- Organization loading process may be starting but not completing
- Possible silent failure in organization loading process

## Implementation Plan

1. **IMMEDIATE: Investigate Auth Initialization Failure**
   - Check browser console for JavaScript errors
   - Verify AuthProvider is actually rendering in the component tree
   - Add try/catch blocks around initialization code
   - Add explicit console logs at the start of AuthProvider render
   - Check Supabase connection status and auth listener setup
   - Verify React Error Boundary isn't silently catching errors
   - Add temporary debug code:
     ```typescript
     console.log('🔍 AuthProvider render start');
     try {
       // Existing initialization code
       console.log('✅ AuthProvider initialization complete');
     } catch (error) {
       console.error('❌ AuthProvider initialization error:', error);
     }
     ```

2. Analyze the current AuthProvider implementation:
   - Review the component lifecycle and React hooks usage
   - Identify where auth state transitions are triggered
   - Examine the isMounted.current reference implementation
   - Review LoadingStateManager timeout logic and recovery UI triggers

3. Debug the duplicate events issue:
   - Add temporary logging to track auth state transitions:
     ```typescript
     useEffect(() => {
       console.log(`Auth state changed: ${JSON.stringify(authState)}`);
       // Track component mount/unmount cycles
       return () => console.log('Auth state effect cleanup');
     }, [authState]);
     ```
   - Use React DevTools to monitor component re-renders
   - Implement React.Profiler to measure render frequency
   - Add specific logging for system admin organization loading

4. Fix the identified issues:
   - Address React Strict Mode compatibility with a better mount pattern:
     ```typescript
     // Replace this pattern
     const isMounted = useRef(false);
     useEffect(() => {
       isMounted.current = true;
       return () => { isMounted.current = false; };
     }, []);
     
     // With a more robust pattern that works in Strict Mode
     const mountedRef = useRef<boolean>(false);
     const isMounted = useCallback(() => mountedRef.current, []);
     const executeSafely = useCallback((callback: Function) => {
       if (mountedRef.current) callback();
     }, []);
     
     useEffect(() => {
       mountedRef.current = true;
       return () => { mountedRef.current = false; };
     }, []);
     ```

   - Fix race conditions in auth state management with event deduplication:
     ```typescript
     // Implement proper state transition guards
     const handleAuthStateChange = useCallback((event, session) => {
       // Prevent duplicate transitions for the same event/session
       if (
         prevEventRef.current === event && 
         prevSessionRef.current?.id === session?.id
       ) {
         console.debug('Skipping duplicate auth event', event);
         return;
       }
       
       prevEventRef.current = event;
       prevSessionRef.current = session;
       
       // Process auth state change
       dispatch({ type: event, session });
     }, [dispatch]);
     ```

   - Consolidate organization loading to a single location:
     ```typescript
     // Remove loadOrganizationData from initializeAuth()
     // Only call it from onAuthStateChange when user is signed in
     
     const onAuthStateChange = useCallback((event, session) => {
       // Handle auth state change...
       
       if (event === 'SIGNED_IN' && session?.user?.id) {
         // Use a ref to track if we've loaded org for this user
         if (loadedOrgForUserRef.current !== session.user.id) {
           loadedOrgForUserRef.current = session.user.id;
           loadOrganizationData(session.user.id);
         }
       } else if (event === 'SIGNED_OUT') {
         // Reset the tracking ref on sign out
         loadedOrgForUserRef.current = null;
       }
     }, []);
     ```

   - Fix dependency array in loadOrganizationData:
     ```typescript
     // Instead of this:
     useEffect(() => {
       loadOrganizationData(session?.user?.id);
     }, [session?.user?.id]);
     
     // Create a stable function with proper dependencies:
     const loadOrganizationData = useCallback((userId: string | undefined) => {
       if (!userId) return;
       // Load organization logic...
     }, [dispatch]); // Only depend on dispatch, not user ID
     ```

   - Fix LoadingStateManager timeout and recovery issues:
     ```typescript
     // Adjust timeout for auth loading
     const LOADING_TIMEOUT = 30000; // Increase from 15s to 30s for initial auth
     
     // Add better state tracking for system admin detection
     const isSystemAdmin = useCallback((user) => {
       // Improved system admin detection logic
       console.debug('Checking system admin status:', user);
       return user?.app_metadata?.claims_admin === true;
     }, []);
     
     // Add explicit state transition for system admins
     if (isSystemAdmin(session?.user)) {
       console.debug('System admin detected, setting special state');
       dispatch({ type: 'SYSTEM_ADMIN_DETECTED', session });
     }
     ```

5. Refactor the AuthProvider component:
   - Separate concerns with custom hooks
   - Implement proper cleanup in useEffect hooks
   - Use functional updates for state changes
   - Add proper error boundaries
   - Improve system admin organization loading logic

6. Document the changes:
   - Add comments explaining the race condition fixes
   - Document the component lifecycle and state management approach
   - Update any related documentation
   - Document LoadingStateManager timeout adjustments

7. Fix the critical 'authenticatedNoOrg' stuck state:
   - Add comprehensive logging to track organization loading process:
     ```typescript
     const loadOrganizationData = useCallback(async (userId: string | undefined) => {
       if (!userId) {
         console.error('🔍 loadOrganizationData called with no userId');
         return;
       }
       
       console.log('🔄 Starting organization load for user:', userId);
       try {
         const orgData = await fetchOrganizationData(userId);
         console.log('✅ Organization data loaded successfully:', orgData);
         
         if (isMounted()) {
           console.log('🔀 Dispatching ORGANIZATION_LOADED event');
           dispatch({ type: 'ORGANIZATION_LOADED', organization: orgData });
           
           // Verify state transition
           console.log('🔍 Auth state after org load:', authState);
         } else {
           console.warn('⚠️ Component unmounted, skipping dispatch');
         }
       } catch (error) {
         console.error('❌ Error loading organization:', error);
         
         if (isMounted()) {
           console.log('🔀 Dispatching ORGANIZATION_LOAD_ERROR event');
           dispatch({ type: 'ORGANIZATION_LOAD_ERROR', error });
         }
       }
     }, [dispatch, isMounted, authState]);
     ```

   - Add explicit state transition verification:
     ```typescript
     // Add a useEffect to monitor auth state transitions
     useEffect(() => {
       console.log('🔄 Auth state changed:', authState);
       
       // Check for stuck states
       if (authState.status === 'authenticatedNoOrg' && authState.user) {
         console.log('🔍 In authenticatedNoOrg state with user:', authState.user.id);
         
         // Check if organization loading was attempted
         if (!loadedOrgForUserRef.current) {
           console.warn('⚠️ Organization loading not attempted for user:', authState.user.id);
           loadOrganizationData(authState.user.id);
         } else if (loadedOrgForUserRef.current !== authState.user.id) {
           console.warn('⚠️ Organization loaded for different user:', loadedOrgForUserRef.current);
           loadOrganizationData(authState.user.id);
         } else {
           console.log('✅ Organization loading was attempted for current user');
         }
       }
     }, [authState, loadOrganizationData]);
     ```

   - Fix state machine transitions for organization loading:
     ```typescript
     // In the auth reducer
     case 'ORGANIZATION_LOADED':
       console.log('🔄 Processing ORGANIZATION_LOADED event');
       console.log('🔍 Current state:', state);
       console.log('🔍 Organization data:', action.organization);
       
       // Ensure we actually transition to authenticated state
       return {
         ...state,
         status: 'authenticated', // Explicitly set to authenticated
         organization: action.organization,
         loading: false
       };
     ```

   - Add fallback for system admins:
     ```typescript
     // Special handling for system admins
     if (isSystemAdmin(authState.user) && authState.status === 'authenticatedNoOrg') {
       console.log('🔑 System admin detected in authenticatedNoOrg state');
       console.log('🔄 Forcing transition to authenticated state');
       
       // Force transition to authenticated state for system admins
       dispatch({ 
         type: 'FORCE_AUTHENTICATED', 
         user: authState.user,
         // Use empty organization for system admins if needed
         organization: { id: 'system', name: 'System', role: 'admin' }
       });
     }
     ```

## Implemented Fixes

**1. Fixed React Strict Mode Compatibility**
- Replaced `isMounted.current` pattern with `mountedRef.current` and `isMounted()` callback
- This prevents the "isMounted.current: false" issue during normal operation
- Component now properly tracks mount state even with React Strict Mode double-mounting

**2. Implemented Event Deduplication**
- Added `prevAuthEventRef` to track previous auth events and prevent duplicates
- SIGN_IN events are now deduplicated based on event type and user ID
- Added debug logging to show when duplicate events are skipped

**3. Consolidated Organization Loading**
- Removed duplicate `loadOrganizationData()` call from `initializeAuth()`
- Organization loading now only happens in `onAuthStateChange()` with proper guards
- Added session validation to ensure we have current data before loading

**4. Fixed Dependency Array Issues**
- Changed `loadOrganizationData` dependency from `[session?.user?.id]` to `[authState, isMounted]`
- Main useEffect now has empty dependency array `[]` to prevent re-initialization
- This eliminates the re-render cycles that were causing duplicate calls

**5. Enhanced Error Handling**
- All state updates now check `isMounted()` before dispatching
- Proper cleanup of tracking refs on sign out
- Better session validation before organization loading

**6. Enhanced Debugging**
- Added detailed emoji-based logging throughout the organization loading process
- Implemented auth state machine logging with clear before/after states
- Added visual indicators for successful transitions and ignored events
- Specific logging for system admin detection and organization assignment

**7. Adjusted LoadingStateManager Timeouts**
- Recovery button now appears after 10 seconds (was 3 seconds)
- Manual recovery UI appears after 20 seconds total (was 8 seconds)
- Gives more time for auth process to complete naturally
- Reduces false positive recovery UI appearances

**8. Fixed Critical 'authenticatedNoOrg' Stuck State**
- Added comprehensive logging to track organization loading process
- Implemented explicit state transition verification
- Fixed state machine transitions for organization loading
- Added fallback mechanism for system admins
- Added safety checks to ensure state transitions complete properly

# Test Strategy:
1. Create a comprehensive test suite for the AuthProvider:
   - Write unit tests using React Testing Library to verify correct state transitions
   - Test component mounting/unmounting behavior
   - Simulate auth state changes and verify correct handling

2. Implement specific test cases for the identified issues:
   - Test for duplicate SIGN_IN events:
     ```typescript
     test('should not trigger duplicate SIGN_IN events', async () => {
       const mockDispatch = jest.fn();
       const { rerender } = render(<AuthStateHandler dispatch={mockDispatch} />);
       
       // Simulate auth state change
       act(() => {
         authStateChange('SIGNED_IN', mockSession);
       });
       
       // Simulate the same auth state change again
       act(() => {
         authStateChange('SIGNED_IN', mockSession);
       });
       
       // Should only dispatch once for the same session
       expect(mockDispatch).toHaveBeenCalledTimes(1);
     });
     ```

   - Test for proper mount state handling in Strict Mode:
     ```typescript
     test('should properly track mounted state in Strict Mode', async () => {
       // Enable mock strict mode behavior (double-mount)
       const strictModeRoot = document.createElement('div');
       const onMountAction = jest.fn();
       
       act(() => {
         // Simulate strict mode by mounting twice
         const { unmount: unmount1 } = render(<MountStateComponent onMountAction={onMountAction} />, { container: strictModeRoot });
         unmount1(); // First unmount in strict mode cycle
         render(<MountStateComponent onMountAction={onMountAction} />, { container: strictModeRoot });
       });
       
       // Should still execute mount action correctly despite strict mode
       expect(onMountAction).toHaveBeenCalledTimes(1);
     });
     ```

   - Test for consolidated organization loading:
     ```typescript
     test('should only load organization data once per user session', async () => {
       const loadOrgFn = jest.fn();
       const { rerender } = render(
         <AuthProvider loadOrganizationData={loadOrgFn} />
       );
       
       // Simulate sign in
       act(() => {
         authStateChange('SIGNED_IN', { user: { id: 'user-123' } });
       });
       
       // Simulate the same sign in again
       act(() => {
         authStateChange('SIGNED_IN', { user: { id: 'user-123' } });
       });
       
       // Should only load org data once for the same user
       expect(loadOrgFn).toHaveBeenCalledTimes(1);
       expect(loadOrgFn).toHaveBeenCalledWith('user-123');
     });
     ```

   - Test for dependency array fix:
     ```typescript
     test('should not reload organization when component rerenders', async () => {
       const loadOrgFn = jest.fn();
       const { rerender } = render(
         <AuthProvider loadOrganizationData={loadOrgFn} someOtherProp="initial" />
       );
       
       // Simulate sign in
       act(() => {
         authStateChange('SIGNED_IN', { user: { id: 'user-123' } });
       });
       
       // Should have called once
       expect(loadOrgFn).toHaveBeenCalledTimes(1);
       
       // Rerender with different props (but same user)
       rerender(<AuthProvider loadOrganizationData={loadOrgFn} someOtherProp="changed" />);
       
       // Should still only have called once
       expect(loadOrgFn).toHaveBeenCalledTimes(1);
     });
     ```

   - Test for system admin organization loading:
     ```typescript
     test('should correctly handle system admin organization loading', async () => {
       const loadOrgFn = jest.fn();
       const { rerender } = render(
         <AuthProvider loadOrganizationData={loadOrgFn} />
       );
       
       // Simulate system admin sign in
       act(() => {
         authStateChange('SIGNED_IN', { 
           user: { 
             id: 'admin-123',
             app_metadata: { claims_admin: true } 
           } 
         });
       });
       
       // Should handle system admin correctly
       expect(loadOrgFn).toHaveBeenCalledTimes(1);
       // Verify correct state transition for system admin
       expect(mockDispatch).toHaveBeenCalledWith({
         type: 'SYSTEM_ADMIN_DETECTED',
         session: expect.any(Object)
       });
     });
     ```

   - Test for LoadingStateManager timeout handling:
     ```typescript
     test('should handle loading timeouts correctly', async () => {
       jest.useFakeTimers();
       const { getByText, queryByText } = render(
         <LoadingStateManager>
           <AuthProvider />
         </LoadingStateManager>
       );
       
       // Initial loading state
       expect(getByText("Connecting to your account...")).toBeInTheDocument();
       
       // Fast-forward past the timeout
       jest.advanceTimersByTime(20000);
       
       // Should show recovery UI
       expect(getByText("This is taking longer than usual")).toBeInTheDocument();
       
       // Simulate auth completion
       act(() => {
         authStateChange('SIGNED_IN', { user: { id: 'user-123' } });
         // Complete organization loading
         mockLoadOrganizationComplete();
       });
       
       // Recovery UI should be gone
       expect(queryByText("This is taking longer than usual")).not.toBeInTheDocument();
       
       jest.useRealTimers();
     });
     ```

   - Test for authenticatedNoOrg to authenticated transition:
     ```typescript
     test('should properly transition from authenticatedNoOrg to authenticated', async () => {
       const mockDispatch = jest.fn();
       const { rerender } = render(
         <AuthProvider initialState={{ status: 'authenticatedNoOrg', user: { id: 'user-123' } }} dispatch={mockDispatch} />
       );
       
       // Simulate organization loaded event
       act(() => {
         mockDispatch({ type: 'ORGANIZATION_LOADED', organization: { id: 'org-123' } });
       });
       
       // Should transition to authenticated state
       expect(mockAuthState.status).toBe('authenticated');
       expect(mockAuthState.organization).toEqual({ id: 'org-123' });
     });
     ```

   - Test for system admin fallback mechanism:
     ```typescript
     test('should have fallback for system admins in authenticatedNoOrg state', async () => {
       const mockDispatch = jest.fn();
       const { rerender } = render(
         <AuthProvider 
           initialState={{ 
             status: 'authenticatedNoOrg', 
             user: { 
               id: 'admin-123', 
               app_metadata: { claims_admin: true } 
             } 
           }} 
           dispatch={mockDispatch} 
         />
       );
       
       // Should detect system admin and force transition
       expect(mockDispatch).toHaveBeenCalledWith({
         type: 'FORCE_AUTHENTICATED',
         user: expect.objectContaining({ id: 'admin-123' }),
         organization: expect.objectContaining({ id: 'system' })
       });
     });
     ```

   - Test for AuthProvider initialization:
     ```typescript
     test('should properly initialize and set up auth listeners', async () => {
       // Mock Supabase client
       const mockSupabase = {
         auth: {
           onAuthStateChange: jest.fn(),
           getSession: jest.fn().mockResolvedValue({ data: { session: null } })
         }
       };
       
       // Render with mocked client
       render(<AuthProvider supabaseClient={mockSupabase} />);
       
       // Should set up auth listener
       expect(mockSupabase.auth.onAuthStateChange).toHaveBeenCalled();
       expect(mockSupabase.auth.getSession).toHaveBeenCalled();
     });
     ```

3. Implement integration tests:
   - Test the full authentication flow
   - Verify organization loading occurs only once per auth session
   - Test React Strict Mode compatibility
   - Test system admin authentication flow specifically

4. Performance testing:
   - Use React Profiler to measure render counts before and after fixes
   - Verify reduced number of renders and state transitions
   - Measure time to complete auth flow for system admins

5. Manual verification:
   - Test in development environment with React Strict Mode enabled
   - Monitor browser console for duplicate events
   - Verify admin dashboard loads correctly without duplicate events
   - Test recovery UI functionality
   - Verify system admin authentication works correctly

6. Document test results:
   - Create a report showing the reduction in duplicate events
   - Document any edge cases discovered during testing
   - Document LoadingStateManager timeout behavior

7. Verify implemented fixes:
   - Test the application to verify duplicate events are eliminated
   - Monitor console logs for auth state transitions
   - Verify system admin dashboard loads correctly
   - Confirm TypeScript compilation passes with no errors
   - Verify recovery UI appears and disappears appropriately
   
8. Test enhanced debugging features:
   - Verify emoji-based logging appears correctly in the console
   - Check that auth state transitions show clear before/after states
   - Confirm system admin detection logging works as expected
   - Validate that the adjusted LoadingStateManager timeouts function correctly
   
9. Test critical 'authenticatedNoOrg' stuck state fix:
   - Verify application successfully transitions from 'authenticatedNoOrg' to 'authenticated'
   - Check console logs for organization loading process completion
   - Confirm system admin fallback mechanism works correctly
   - Verify recovery UI does not appear when auth flow completes normally
   
10. Test AuthProvider initialization:
    - Add explicit console logs at the start of AuthProvider render
    - Check for JavaScript errors in the browser console
    - Verify AuthProvider is actually rendering in the component tree
    - Test Supabase connection status and auth listener setup

# Subtasks:
## 21.1. Verify fixes in development environment [todo]
### Dependencies: None
### Description: Test the application with the implemented fixes to confirm that duplicate auth events are eliminated and the admin dashboard loads correctly.
### Details:


## 21.2. Document implementation details [todo]
### Dependencies: None
### Description: Create documentation explaining the fixes implemented, including the React Strict Mode compatibility pattern, event deduplication logic, and organization loading consolidation.
### Details:


## 21.3. Create regression tests [todo]
### Dependencies: None
### Description: Develop regression tests to ensure the fixes remain effective in future updates, focusing on mount state handling, event deduplication, and dependency array optimizations.
### Details:


## 21.4. Fix LoadingStateManager timeout issues [todo]
### Dependencies: None
### Description: Investigate and fix the LoadingStateManager timeout issues causing the recovery UI to appear. Adjust timeout values and improve state transition handling for system admin users.
### Details:


## 21.5. Improve system admin organization detection [todo]
### Dependencies: None
### Description: Enhance the logic for detecting and handling system admin users to prevent stuck loading states. Add explicit state transitions and debug logging for system admin authentication flow.
### Details:


## 21.6. Test recovery mechanism [todo]
### Dependencies: None
### Description: Verify that the recovery UI functions correctly when auth loading takes too long, and that it disappears appropriately when authentication completes successfully.
### Details:


## 21.7. Review enhanced debugging logs [todo]
### Dependencies: None
### Description: Test the application and review the enhanced emoji-based console logs to identify the root cause of loading state issues. Pay special attention to auth state transitions and system admin detection.
### Details:


## 21.8. Verify adjusted LoadingStateManager timeouts [todo]
### Dependencies: None
### Description: Test the application to confirm that the adjusted LoadingStateManager timeouts (10s for recovery button, 20s for manual recovery UI) reduce false positive recovery UI appearances.
### Details:


## 21.9. Fix critical 'authenticatedNoOrg' stuck state [todo]
### Dependencies: None
### Description: Implement and test fixes for the critical issue where the application gets stuck in 'authenticatedNoOrg' state. Add comprehensive logging, explicit state transition verification, and a fallback mechanism for system admins.
### Details:


## 21.10. Verify organization loading process completion [todo]
### Dependencies: None
### Description: Add detailed logging to track the organization loading process from start to finish. Verify that the ORGANIZATION_LOADED event is properly dispatched and processed, resulting in a transition to 'authenticated' state.
### Details:


## 21.11. Implement system admin fallback mechanism [todo]
### Dependencies: None
### Description: Create a special fallback mechanism for system admin users that forces a transition to 'authenticated' state with a default organization if the normal organization loading process fails or takes too long.
### Details:


## 21.12. Investigate auth initialization failure [todo]
### Dependencies: None
### Description: Investigate why auth initialization isn't starting at all. Check for JavaScript errors in the browser console, verify AuthProvider is properly imported/rendered, check Supabase connection issues, and look for silent errors in React Error Boundary.
### Details:


## 21.13. Add initialization debugging [todo]
### Dependencies: None
### Description: Add explicit console logs at the start of AuthProvider render and wrap initialization code in try/catch blocks to capture any errors that might be preventing auth initialization.
### Details:


## 21.14. Verify Supabase auth listener setup [todo]
### Dependencies: None
### Description: Check if the Supabase auth state change listener is being properly set up. Add logging to verify the onAuthStateChange callback is registered and being called when auth state changes.
### Details:


