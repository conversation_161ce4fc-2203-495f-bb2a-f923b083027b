# Task ID: 5
# Title: Implement Organization Health Indicators
# Status: pending
# Dependencies: 3
# Priority: medium
# Description: Develop a system for calculating and displaying health indicators for each organization.
# Details:
1. Create `useOrganizationHealth.ts` hook in `/src/hooks/`
2. Define health indicator criteria (e.g., patient volume, appointment efficiency, data completeness)
3. Implement health calculation algorithm:
   ```typescript
   function calculateHealthScore(metrics: OrganizationMetrics): HealthScore {
     // Algorithm implementation
   }
   ```
4. Create `OrganizationHealthIndicator` component
5. Use color-coding (green/yellow/red) to represent health status
6. Implement tooltips to explain health score calculation
7. Ensure real-time updates of health indicators

# Test Strategy:
1. Unit test health calculation algorithm
2. Test `useOrganizationHealth` hook with various input scenarios
3. Verify correct color representation of health status
4. Test tooltip content and functionality
5. Perform integration tests with SystemOverviewDashboard
