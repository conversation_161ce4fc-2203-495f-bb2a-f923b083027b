import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { PatientStats as PatientStatsType } from "@/types/patient";
import { Activity, Calendar, TrendingUp, Users } from "lucide-react";

interface PatientStatsProps {
  stats: PatientStatsType | null;
  isLoading?: boolean;
}

export function PatientStats({ stats, isLoading }: PatientStatsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted animate-pulse rounded" />
              </CardTitle>
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted animate-pulse rounded mb-1" />
              <div className="h-3 bg-muted animate-pulse rounded w-3/4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const statCards = [
    {
      title: "Total Patients",
      value: stats.total.toLocaleString(),
      description: `${stats.recentlyAdded} added this month`,
      icon: Users,
      trend: stats.recentlyAdded > 0 ? "up" : "neutral",
    },
    {
      title: "Active Patients",
      value: stats.active.toLocaleString(),
      description: `${Math.round((stats.active / stats.total) * 100)}% of total`,
      icon: Activity,
      trend: stats.active > stats.inactive ? "up" : "down",
    },
    {
      title: "Upcoming Appointments",
      value: stats.withUpcomingAppointments.toLocaleString(),
      description: "Scheduled appointments",
      icon: Calendar,
      trend: "neutral",
    },
    {
      title: "Average Age",
      value: Math.round(stats.averageAge).toString(),
      description: "Years old",
      icon: TrendingUp,
      trend: "neutral",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
