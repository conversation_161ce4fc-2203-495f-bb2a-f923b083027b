# CI/CD Setup for Spritely

This document outlines the CI/CD setup for the <PERSON><PERSON><PERSON>ly project, including how to manage Supabase credentials securely across different environments.

## Overview

The CI/CD pipeline is configured to:

1. Automatically deploy database changes to the appropriate Supabase environment
2. Build and deploy the application to the appropriate hosting environment
3. Securely manage credentials using 1Password

## Environment Structure

The project supports multiple environments:

- **Local**: For local development
- **Development**: Connected to the development Supabase instance (dev branch)
- **Staging**: Connected to the staging Supabase instance (staging branch)
- **Production**: Connected to the production Supabase instance (master branch)

## 1Password Setup

### Initial Setup

1. Install the 1Password CLI:

   ```bash
   # macOS
   brew install 1password-cli

   # Linux
   curl -sS https://downloads.1password.com/linux/keys/1password.asc | \
   sudo gpg --dearmor --output /usr/share/keyrings/1password-archive-keyring.gpg
   echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/1password-archive-keyring.gpg] https://downloads.1password.com/linux/debian/$(dpkg --print-architecture) stable main" | \
   sudo tee /etc/apt/sources.list.d/1password.list
   sudo apt update && sudo apt install 1password-cli
   ```

2. Authenticate with 1Password:

   ```bash
   op signin
   ```

3. Run the vault setup script:

   ```bash
   npm run 1password:setup
   ```

   This will create:

   - A "Spritely" vault
   - Items for each environment (local, development, staging, production)

4. Organize the vault and add credentials:

   ```bash
   npm run 1password:organize
   ```

   This interactive script will:

   - Update Supabase credential items with proper descriptions and tags
   - Create Netlify credential items for each environment
   - Create GitHub credential item
   - Create 1Password service account item for CI/CD

   The script will prompt you for the necessary credentials for each item.

### 1Password Vault Structure

The Spritely vault contains the following items:

1. **Supabase Credentials**

   - `Spritely Supabase local` - Local development environment
   - `Spritely Supabase development` - Development environment (dev branch)
   - `Spritely Supabase staging` - Staging environment (staging branch)
   - `Spritely Supabase production` - Production environment (master branch)

2. **Netlify Credentials**

   - `Spritely Netlify development` - Development environment
   - `Spritely Netlify staging` - Staging environment
   - `Spritely Netlify production` - Production environment

3. **GitHub Credentials**

   - `Spritely GitHub` - GitHub repository access

4. **1Password Service Account**
   - `Spritely 1Password Service Account` - For CI/CD integration

All items are properly tagged with relevant tags (supabase, netlify, github, spritely, ci-cd) for easy filtering and organization.

### CI/CD Integration

For CI/CD, you'll need to create a 1Password service account:

1. Go to 1Password.com and sign in to your account
2. Navigate to Settings > Integrations > Service Accounts
3. Create a new service account
4. Grant it access to the "Spritely" vault
5. Copy the service account token

6. Add the required secrets to your GitHub repository:
   - Go to your GitHub repository
   - Navigate to Settings > Secrets and variables > Actions
   - Add the following repository secrets:
     - `OP_SERVICE_ACCOUNT_TOKEN`: Your 1Password service account token
     - `NETLIFY_AUTH_TOKEN`: Your Netlify authentication token
     - `NETLIFY_SITE_ID`: Your Netlify site ID

## GitHub Actions Workflow

The GitHub Actions workflow is configured to:

1. Determine the environment based on the branch:

   - `master` → production
   - `staging` → staging
   - `dev` → development

2. Deploy database changes:

   - Retrieve credentials from 1Password
   - Link to the appropriate Supabase project
   - Push database changes
   - Generate TypeScript types

3. Build and deploy the application:
   - Retrieve credentials from 1Password
   - Build the application with the appropriate environment variables
   - Deploy to the appropriate hosting environment

## Local Development

For local development, you can use the following commands:

```bash
# Set up local environment
npm run env:local

# Set up development environment
npm run env:development

# Set up staging environment
npm run env:staging

# Set up production environment
npm run env:production
```

## Adding New Environments

To add a new environment:

1. Add a new item in the 1Password vault:

   - Open 1Password
   - Navigate to the "Spritely" vault
   - Create a new item with the name "Spritely Supabase [environment]"
   - Add the required fields: url, project_id, anon_key, service_key

2. Update the GitHub Actions workflow:

   - Add a new branch mapping in the `setup` job
   - Add deployment steps for the new environment

3. Update the Supabase configuration:
   - Add a new remote configuration in `supabase/config.toml`
   - Create environment-specific seed files in `supabase/seeds/environments/[environment]`

## Security Considerations

- Never commit credentials to version control
- Use 1Password for all credential management
- Use environment-specific configurations
- Limit access to production credentials
- Regularly rotate Supabase keys

## Troubleshooting

### 1Password CLI Issues

If you encounter issues with the 1Password CLI:

1. Make sure you're authenticated:

   ```bash
   op account get
   ```

2. If not authenticated, sign in:

   ```bash
   op signin
   ```

3. Check if the vault exists:
   ```bash
   op vault list
   ```

### GitHub Actions Issues

If the GitHub Actions workflow fails:

1. Check if the `OP_SERVICE_ACCOUNT_TOKEN` secret is set correctly
2. Verify that the service account has access to the "Spritely" vault
3. Check if the 1Password items have the correct field names

### Supabase Issues

If Supabase deployment fails:

1. Check if the project ID is correct
2. Verify that the service key has the necessary permissions
3. Check for syntax errors in the SQL files
