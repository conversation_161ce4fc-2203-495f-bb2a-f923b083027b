# Multi-Organization Dashboard Enhancement PRD

## Project Overview
Enhance the existing Spritely healthcare management system dashboard to support multi-organization and "All Organizations" views for system administrators and multi-tenant users.

## Current State
- Dashboard currently designed for single-organization views
- Shows organization-specific data: recent patients, tasks, activity
- Organization selector exists but dashboard doesn't adapt to "All Organizations" selection
- System admins and multi-tenant users need better overview capabilities

## Problem Statement
When system administrators select "All Organizations" or when multi-tenant users need to view data across multiple organizations, the current dashboard shows inappropriate single-organization data or fails to load properly. There's no system-level overview or efficient way to manage multiple organizations.

## Goals
1. Create a hierarchical dashboard system (System → Organization → Facility levels)
2. Provide meaningful system-level metrics and organization health indicators
3. Maintain familiar single-organization dashboard experience
4. Enable efficient navigation between different organizational contexts
5. Support both "All Organizations" overview and individual organization deep-dives

## Target Users
- System Administrators (access to all organizations)
- Multi-tenant users (access to multiple organizations)
- Organization administrators (enhanced context awareness)

## Core Features

### System-Level Dashboard (All Organizations View)
- **System Overview Metrics**
  - Total organizations count
  - Aggregate patient count across all orgs
  - Total active appointments today/this week
  - System-wide critical alerts count
  - Overall system health indicators

- **Organization Performance Grid**
  - Card-based layout showing each organization
  - Per-organization metrics: patient count, appointment volume, health status
  - Visual health indicators (green/yellow/red status)
  - Quick action buttons (View Details, Manage Organization)
  - Search and filter capabilities

- **System-Wide Activity Feed**
  - Cross-organization activity with organization labels
  - Filterable by organization, activity type, time range
  - System-level alerts and notifications
  - Critical issues requiring immediate attention

- **Global Analytics**
  - System performance trends
  - Cross-organization comparisons
  - Resource utilization metrics
  - Growth and usage patterns

### Enhanced Organization Dashboard
- **Context Indicators**
  - Clear breadcrumb navigation showing current scope
  - Organization name and facility context
  - Quick switcher for changing context

- **Facility-Aware Components**
  - Facility selector integration
  - Facility-specific data when applicable
  - Multi-facility aggregation options

### Navigation & UX Improvements
- **Smart Routing**
  - Automatic detection of "All Organizations" selection
  - Context-aware component rendering
  - Smooth transitions between dashboard types

- **Contextual Breadcrumbs**
  - System → Organization → Facility hierarchy
  - Easy navigation between levels
  - Current context highlighting

## Technical Requirements

### New Components
- `SystemOverviewDashboard.tsx` - All Organizations view
- `OrganizationDashboard.tsx` - Refactored current dashboard
- `DashboardContainer.tsx` - Smart router and context manager
- `OrganizationHealthCard.tsx` - Individual org status cards
- `SystemMetricsWidget.tsx` - System-level statistics
- `GlobalActivityFeed.tsx` - Cross-org activity display

### Data Layer
- `useSystemMetrics.ts` - Cross-organization aggregated data
- `useOrganizationHealth.ts` - Health indicators per organization
- `useSystemAlerts.ts` - System-wide notifications and alerts
- `useGlobalActivity.ts` - Cross-organization activity feed
- Enhanced existing hooks for multi-org support

### Database Considerations
- Efficient cross-organization queries
- Proper RLS policies for system admin access
- Aggregation functions for system metrics
- Performance optimization for large datasets

## User Stories

### System Administrator
- As a system admin, I want to see an overview of all organizations so I can quickly identify issues
- As a system admin, I want to see system-wide metrics so I can monitor overall platform health
- As a system admin, I want to drill down into specific organizations without losing context

### Multi-Tenant User
- As a multi-tenant user, I want to see data from all my accessible organizations in one view
- As a multi-tenant user, I want to quickly switch between organization contexts
- As a multi-tenant user, I want to compare performance across my organizations

### Organization Administrator
- As an org admin, I want clear indication of my current organizational context
- As an org admin, I want to understand how my organization fits into the larger system

## Success Metrics
- Reduced time to identify cross-organization issues
- Improved system admin efficiency in managing multiple organizations
- Increased user satisfaction with multi-organization workflows
- Better system health monitoring and alerting

## Implementation Phases

### Phase 1: Foundation
- Create dashboard routing logic
- Implement system metrics data layer
- Build basic SystemOverviewDashboard component

### Phase 2: Organization Grid
- Develop organization health indicators
- Create organization performance cards
- Implement search and filtering

### Phase 3: Activity & Analytics
- Build global activity feed
- Add system-wide analytics
- Implement alert management

### Phase 4: Polish & Optimization
- Performance optimization
- Enhanced UX and animations
- Comprehensive testing

## Constraints
- Must maintain backward compatibility with existing dashboard
- Should leverage existing component library and design system
- Must respect existing RLS policies and security model
- Performance should not degrade for single-organization users

## Dependencies
- Facility selector system (already implemented)
- Organization selector functionality (existing)
- User role and permission system (existing)
- Existing dashboard components and hooks 