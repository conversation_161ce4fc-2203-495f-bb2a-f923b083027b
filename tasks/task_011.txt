# Task ID: 11
# Title: Develop Contextual Breadcrumbs Component
# Status: pending
# Dependencies: 2, 10
# Priority: medium
# Description: Create a breadcrumb navigation component that reflects the current system hierarchy and allows easy navigation between levels.
# Details:
1. Create `ContextualBreadcrumbs.tsx` component
2. Use @mantine/core for base breadcrumb styling
3. Implement dynamic breadcrumb generation based on current context
4. Add click handlers for navigation between levels
5. Implement visual highlighting of current context
6. Ensure proper truncation for long organization or facility names
7. Add tooltips for full names on truncated breadcrumbs
8. Implement keyboard navigation support

# Test Strategy:
1. Unit test breadcrumb generation logic
2. Test navigation functionality between different levels
3. Verify correct highlighting of current context
4. Test truncation and tooltip behavior
5. Ensure accessibility compliance with keyboard navigation tests
