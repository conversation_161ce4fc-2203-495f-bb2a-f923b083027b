# Task ID: 10
# Title: Implement Smart Routing and Context Awareness
# Status: pending
# Dependencies: 2, 9
# Priority: high
# Description: Enhance the dashboard's routing system to automatically detect and handle 'All Organizations' selection and context changes.
# Details:
1. Enhance `DashboardContainer.tsx` with smart routing logic
2. Implement automatic detection of 'All Organizations' selection
3. Create a custom hook for managing dashboard context:
   ```typescript
   function useDashboardContext() {
     // Implementation
   }
   ```
4. Add logic for smooth transitions between dashboard types
5. Implement context persistence using local storage or URL parameters
6. Create HOC or custom hook for injecting context into components
7. Ensure proper handling of direct URL access to specific views

# Test Strategy:
1. Unit test context detection and management functions
2. Test routing behavior for various user roles and selections
3. Verify smooth transitions between different dashboard contexts
4. Test deep linking and direct URL access scenarios
5. Perform integration tests with both system and organization dashboards
