"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLocations } from "@/hooks/useLocations";
import { ALL_LOCATIONS_ID } from "@/types/location";
import type { Json } from "@spritely/supabase-types";
import { Building2, ChevronDown } from "lucide-react";
import { useMemo } from "react";

/**
 * LocationSelector component for switching between user's accessible locations
 * Includes "All Locations" option for users with access to multiple locations
 */
export function LocationSelector() {
  const {
    locations,
    selectedLocation,
    isLoading,
    canSwitchLocations,
    shouldShowLocationSelector,
    handleLocationSelect,
  } = useLocations();

  // Create options including "All Locations" if user has multiple locations
  const locationOptions = useMemo(() => {
    const options = [...locations];

    // Add "All Locations" option if user has access to multiple locations
    if (canSwitchLocations) {
      const allLocationsOption = {
        id: ALL_LOCATIONS_ID,
        organization_id: selectedLocation?.organization_id || "",
        name: "All Locations",
        type: "system" as const,
        address: {},
        contact_info: {},
        operating_hours: null as Json,
        settings: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add at the beginning of the list
      options.unshift(allLocationsOption);
    }

    return options;
  }, [locations, canSwitchLocations, selectedLocation?.organization_id]);

  // Get display name for selected location
  const selectedLocationName = useMemo(() => {
    if (!selectedLocation) return "Select Location";

    if (selectedLocation.id === ALL_LOCATIONS_ID) {
      return "All Locations";
    }

    return selectedLocation.name;
  }, [selectedLocation]);

  // Get icon for selected location
  const selectedLocationIcon = useMemo(() => {
    if (!selectedLocation) return <Building2 className="h-4 w-4" />;

    if (selectedLocation.id === ALL_LOCATIONS_ID) {
      return <Building2 className="h-4 w-4" />;
    }

    return <Building2 className="h-4 w-4" />;
  }, [selectedLocation]);

  // Handle location selection
  const handleLocationClick = (locationId: string) => {
    const location = locationOptions.find((f) => f.id === locationId);
    if (location) {
      handleLocationSelect(location);
    }
  };

  // Don't render if location selector shouldn't be shown
  if (!shouldShowLocationSelector) {
    return null;
  }

  // Loading state
  if (isLoading) {
    return (
      <Button variant="outline" size="sm" disabled>
        <Building2 className="h-4 w-4 mr-2" />
        Loading...
      </Button>
    );
  }

  // No locations available
  if (locations.length === 0) {
    return (
      <Button variant="outline" size="sm" disabled>
        <Building2 className="h-4 w-4 mr-2" />
        No Locations
      </Button>
    );
  }

  // Single location (no dropdown needed, but show current location)
  if (!canSwitchLocations) {
    return (
      <Button variant="outline" size="sm" disabled>
        {selectedLocationIcon}
        <span className="ml-2">{selectedLocationName}</span>
      </Button>
    );
  }

  // Multiple locations (show dropdown)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          {selectedLocationIcon}
          <span className="ml-2">{selectedLocationName}</span>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {locationOptions.map((location) => (
          <DropdownMenuItem
            key={location.id}
            onClick={() => handleLocationClick(location.id)}
            className={
              selectedLocation?.id === location.id
                ? "bg-accent text-accent-foreground"
                : ""
            }
          >
            <Building2 className="h-4 w-4 mr-2" />
            <span>{location.name}</span>
            {selectedLocation?.id === location.id && (
              <span className="ml-auto text-xs text-muted-foreground">
                Current
              </span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
