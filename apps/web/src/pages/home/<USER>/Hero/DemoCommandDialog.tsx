import { Button } from "@/components/ui/button";
import { Dialog, DialogOverlay, DialogPortal } from "@/components/ui/dialog";
import { useAuth } from "@/hooks/useAuth";
import { cn } from "@/lib/utils";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Command as CommandPrimitive } from "cmdk";
import { ArrowRight, BarChart3, Calendar, CalendarPlus, FileText, Lock, Search, SearchX, UserPlus, Users, X } from "lucide-react";
import * as React from "react";
import { useNavigate } from "react-router-dom";

// Custom Dialog Content without the default close button
const DialogContentWithoutCloseButton = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
        className,
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
));
DialogContentWithoutCloseButton.displayName = "DialogContentWithoutCloseButton";

interface DemoCommandDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  commandKey: string;
}

export function DemoCommandDialog({ isOpen, onOpenChange, commandKey }: DemoCommandDialogProps) {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [query, setQuery] = React.useState("");

  const handleAction = (action: string) => {
    onOpenChange(false);

    if (!user) {
      // For guests, redirect to registration with context
      navigate(`/register?feature=${action}`);
      return;
    }

    // For logged-in users, navigate to actual features
    switch (action) {
      case "patients":
        navigate("/dashboard?tab=patients");
        break;
      case "appointments":
        navigate("/dashboard?tab=appointments");
        break;
      case "notes":
        navigate("/dashboard?tab=notes");
        break;
      case "analytics":
        navigate("/dashboard?tab=analytics");
        break;
      case "new-patient":
        navigate("/dashboard/patients/new");
        break;
      case "new-appointment":
        navigate("/dashboard/appointments/new");
        break;
      case "dashboard":
        navigate("/dashboard");
        break;
      case "settings":
        navigate("/settings");
        break;
      default:
        navigate("/dashboard");
    }
  };

  const guestCommands = [
    {
      id: "demo-patients",
      name: "Patient Management",
      description: "See how you'd manage patient records",
      action: () => handleAction("patients"),
      shortcut: "P",
      icon: Users,
    },
    {
      id: "demo-appointments",
      name: "Appointment Scheduling",
      description: "Experience our scheduling system",
      action: () => handleAction("appointments"),
      shortcut: "A",
      icon: Calendar,
    },
    {
      id: "demo-notes",
      name: "Clinical Documentation",
      description: "Try our note-taking features",
      action: () => handleAction("notes"),
      shortcut: "N",
      icon: FileText,
    },
    {
      id: "demo-analytics",
      name: "Practice Analytics",
      description: "View sample practice insights",
      action: () => handleAction("analytics"),
      shortcut: "D",
      icon: BarChart3,
    },
  ];

  const userCommands = [
    {
      id: "patients",
      name: "Search Patients",
      description: "Find and manage patient records",
      action: () => handleAction("patients"),
      shortcut: "P",
      icon: Users,
    },
    {
      id: "appointments",
      name: "View Appointments",
      description: "Schedule and manage appointments",
      action: () => handleAction("appointments"),
      shortcut: "A",
      icon: Calendar,
    },
    {
      id: "notes",
      name: "Clinical Notes",
      description: "Create and edit clinical documentation",
      action: () => handleAction("notes"),
      shortcut: "N",
      icon: FileText,
    },
    {
      id: "analytics",
      name: "Analytics Dashboard",
      description: "View practice performance metrics",
      action: () => handleAction("analytics"),
      shortcut: "D",
      icon: BarChart3,
    },
    {
      id: "new-patient",
      name: "New Patient",
      description: "Register a new patient",
      action: () => handleAction("new-patient"),
      shortcut: "J",
      icon: UserPlus,
    },
    {
      id: "new-appointment",
      name: "New Appointment",
      description: "Schedule a new appointment",
      action: () => handleAction("new-appointment"),
      shortcut: "B",
      icon: CalendarPlus,
    },
  ];

  const commands = user ? userCommands : guestCommands;

  // Handle keyboard shortcuts when dialog is open
  React.useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Check for individual command shortcuts
      if ((e.metaKey || e.ctrlKey)) {
        const key = e.key.toLowerCase();
        const command = commands.find(cmd => cmd.shortcut.toLowerCase() === key);
        if (command) {
          e.preventDefault();
          command.action();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, commands]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContentWithoutCloseButton className="p-0 gap-0 max-w-[640px] w-full rounded-xl">
        <CommandPrimitive
          className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-14 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5 rounded-xl"
          shouldFilter={true}
          value={query}
          onValueChange={setQuery}
        >
          <div
            className="flex items-center border-b px-4 py-2"
            cmdk-input-wrapper=""
          >
            <Search className="mr-2 h-5 w-5 shrink-0 text-primary" />
            <CommandPrimitive.Input
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-base outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              placeholder={user ? "Search commands..." : "Try our features..."}
              value={query}
              onValueChange={setQuery}
              autoFocus
            />
            <div className="flex items-center gap-2">
              <kbd className="hidden xs:flex h-5 select-none items-center gap-1 rounded border bg-background px-1.5 font-mono text-[10px] font-medium opacity-100 sm:text-xs">
                <span className="text-xs">{commandKey}</span>K
              </kbd>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 rounded-full hover:bg-primary/10"
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <CommandPrimitive.List className="max-h-[400px] overflow-y-auto overflow-x-hidden p-2">
            <CommandPrimitive.Empty className="py-6 text-center text-sm">
              <div className="flex flex-col items-center justify-center gap-1">
                <SearchX className="h-10 w-10 text-muted-foreground/50" />
                <p className="text-muted-foreground">No results found.</p>
                <p className="text-xs text-muted-foreground/70">
                  Try a different search term
                </p>
              </div>
            </CommandPrimitive.Empty>

            <CommandPrimitive.Group
              heading={user ? "Available Commands" : "Try These Features"}
              className="pb-2"
            >
              {commands.map((command) => (
                <CommandPrimitive.Item
                  key={command.id}
                  value={`${command.name} ${command.description}`}
                  onSelect={command.action}
                  className="group relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground aria-selected:bg-accent aria-selected:text-accent-foreground transition-colors"
                >
                  <div className="flex h-7 w-7 items-center justify-center rounded-full bg-primary/10 mr-3 group-hover:bg-primary/20 transition-colors">
                    {user ? (
                      <command.icon className="h-4 w-4 text-primary" />
                    ) : (
                      <div className="relative">
                        <command.icon className="h-4 w-4 text-primary/60" />
                        <Lock className="h-2 w-2 text-primary absolute -bottom-0.5 -right-0.5" />
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col flex-1">
                    <span className="font-medium">{command.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {command.description}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    {!user && (
                      <ArrowRight className="h-3 w-3 text-muted-foreground" />
                    )}
                    <kbd className="flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:text-xs">
                      {commandKey} + {command.shortcut}
                    </kbd>
                  </div>
                </CommandPrimitive.Item>
              ))}
            </CommandPrimitive.Group>

            <div className="py-2 px-2 text-xs text-muted-foreground border-t mt-2">
              <div className="flex items-center justify-center">
                <span>
                  {user
                    ? `Press ${commandKey} + K to access this anywhere in the app`
                    : "Sign up to unlock all features and real functionality"
                  }
                </span>
              </div>
            </div>
          </CommandPrimitive.List>
        </CommandPrimitive>
      </DialogContentWithoutCloseButton>
    </Dialog>
  );
}
