import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/hooks/useAuth";
import { AlertCircle, Loader2, LogOut, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";

interface LoadingScreenProps {
  isPostLoginLoading: boolean;
  showRecoveryButton: boolean;
  handleBackToLogin: () => void;
  handleReloadApplication: () => void;
}

export function LoadingScreen({
  isPostLoginLoading,
  showRecoveryButton,
  handleBackToLogin,
  handleReloadApplication,
}: LoadingScreenProps) {
  const { user, authState, organization } = useAuth();
  const [progress, setProgress] = useState(0);
  const [loadingStep, setLoadingStep] = useState("Initializing...");

  // Simulate progress and update loading steps
  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 90) return prev; // Don't complete until actually done
        return prev + Math.random() * 10;
      });
    }, 500);

    return () => clearInterval(timer);
  }, []);

  // Update loading step based on auth state
  useEffect(() => {
    if (authState.status === "initializing") {
      setLoadingStep("Connecting to Spritely...");
      setProgress(10);
    } else if (authState.status === "authenticatedNoOrg") {
      setLoadingStep("Loading your organization...");
      setProgress(60);
    } else if (user && !organization) {
      setLoadingStep("Setting up your dashboard...");
      setProgress(80);
    } else if (user && organization) {
      setLoadingStep("Finalizing your workspace...");
      setProgress(95);
    }
  }, [authState.status, user, organization]);

  // Display appropriate loading message based on current state
  const getStatusMessage = () => {
    if (authState.status === "initializing") return "Establishing secure connection...";
    if (authState.status === "authenticatedNoOrg") return "Verifying your access permissions...";
    if (user && !organization && isPostLoginLoading) return "Preparing your healthcare workspace...";
    if (user && organization) return "Loading your practice data...";
    return "Starting Spritely Health Platform...";
  };

  const statusMessage = getStatusMessage();

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-background to-blue-600/5" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-600/10 rounded-full blur-3xl" />
      </div>

      <Card className="relative w-full max-w-md shadow-xl border bg-card/95 backdrop-blur-sm">
        <CardContent className="p-8">
          <div className="text-center space-y-6">
            {/* Spritely Logo */}
            <div className="mx-auto w-20 h-20 relative">
              <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500 to-blue-600 rounded-2xl transform rotate-45 shadow-lg" />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-white font-bold text-2xl transform -rotate-45">S</span>
              </div>
              {/* Subtle glow effect */}
              <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500 to-blue-600 rounded-2xl transform rotate-45 blur-md opacity-30 -z-10" />
            </div>

            {/* Main heading */}
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent mb-2">
                Spritely
              </h1>
              <p className="text-muted-foreground text-sm">
                {statusMessage}
              </p>
            </div>

            {/* Progress section */}
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-3">
                <div className="relative">
                  <Loader2 className="w-5 h-5 text-emerald-500 animate-spin" />
                  <div className="absolute inset-0 w-5 h-5 border-2 border-emerald-500/20 rounded-full" />
                </div>
                <span className="text-sm font-medium text-foreground">
                  {loadingStep}
                </span>
              </div>
              
              <div className="space-y-2">
                <Progress 
                  value={progress} 
                  className="h-2 bg-muted" 
                />
                <p className="text-xs text-muted-foreground">
                  This usually takes just a few seconds...
                </p>
              </div>
            </div>

            {/* Recovery section */}
            {showRecoveryButton && (
              <div className="pt-4 border-t border-border space-y-4">
                <div className="flex items-center justify-center space-x-2 text-amber-500">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Taking longer than expected
                  </span>
                </div>
                
                <div className="flex space-x-3">
                  <Button
                    onClick={handleReloadApplication}
                    variant="outline"
                    size="sm"
                    className="flex-1 border-emerald-500/20 hover:bg-emerald-500/10 hover:border-emerald-500/40"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Retry
                  </Button>
                  <Button
                    onClick={handleBackToLogin}
                    variant="ghost"
                    size="sm"
                    className="flex-1 hover:bg-muted"
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
                
                <p className="text-xs text-muted-foreground">
                  If this continues, try refreshing your browser or contact support
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
