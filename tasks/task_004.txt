# Task ID: 4
# Title: Create System Overview Dashboard Component
# Status: pending
# Dependencies: 2, 3
# Priority: high
# Description: Develop the main component for the 'All Organizations' view, displaying system-wide metrics and health indicators.
# Details:
1. Create `SystemOverviewDashboard.tsx` in `/src/components/dashboard/`
2. Use @mantine/core for layout and UI components:
   ```typescript
   import { Grid, Card, Text, Group } from '@mantine/core';
   ```
3. Implement responsive grid layout for metric cards
4. Create subcomponents for each metric type:
   - SystemMetricsWidget
   - CriticalAlertsWidget
   - SystemHealthIndicator
5. Integrate with `useSystemMetrics` hook for data
6. Implement skeleton loaders for better UX during data fetching
7. Add refresh functionality to update metrics on demand

# Test Strategy:
1. Unit test individual subcomponents
2. Integration test SystemOverviewDashboard with mock data
3. Test responsiveness across different screen sizes
4. Verify correct data display and formatting
5. Test refresh functionality and loading states
