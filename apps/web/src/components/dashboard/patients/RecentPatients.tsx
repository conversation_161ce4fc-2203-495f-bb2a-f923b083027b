import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { usePatients } from "@/hooks/patients/usePatients";
import { CalendarClock, Eye, FileEdit } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function RecentPatients() {
  const { patients, isLoading, error } = usePatients({
    limit: 4,
    includeAppointments: true,
  });
  const navigate = useNavigate();

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part.charAt(0))
      .join("")
      .toUpperCase();
  };

  // Function to get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case "Follow-up":
        return "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300";
      case "New Patient":
        return "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300";
      case "Lab Results":
        return "bg-amber-100 text-amber-600 dark:bg-amber-900 dark:text-amber-300";
      case "Prescription":
        return "bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300";
      case "Upcoming Appointment":
        return "bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300";
      case "Cancelled Appointment":
        return "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  // Function to format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Never";

    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">
              Recent Patients
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="flex items-center justify-center p-6">
            <p className="text-sm text-red-500">
              Failed to load recent patients
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">
              Recent Patients
            </CardTitle>
            <Skeleton className="h-8 w-16" />
          </div>
          <CardDescription>
            <Skeleton className="h-4 w-40" />
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Recent Patients</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-xs"
            onClick={() => navigate("/patients")}
          >
            View All
          </Button>
        </div>
        <CardDescription>Recently added or updated patients</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {patients.length === 0 ? (
          <div className="flex items-center justify-center p-6">
            <p className="text-sm text-muted-foreground">
              No recent patients found
            </p>
          </div>
        ) : (
          <div className="divide-y">
            {patients.map((patient) => (
              <div
                key={patient.id}
                className="flex items-center justify-between p-4 hover:bg-muted/50"
              >
                <div className="flex items-center gap-4">
                  <Avatar>
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {getInitials(
                        `${patient.first_name} ${patient.last_name}`,
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{patient.first_name} {patient.last_name}</p>
                    <div className="flex items-center gap-2">
                      <p className="text-xs text-muted-foreground">
                        DOB: {patient.date_of_birth ? new Date(patient.date_of_birth).toLocaleDateString() : 'N/A'}
                      </p>
                      {patient.last_appointment_date && (
                        <div className="flex items-center text-xs text-muted-foreground">
                          <CalendarClock className="h-3 w-3 mr-1" />
                          {formatDate(patient.last_appointment_date)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {patient.next_appointment_date && (
                    <Badge variant="outline" className="bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300">
                      Upcoming Appt
                    </Badge>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => navigate(`/patients/${patient.id}`)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => navigate(`/patients/${patient.id}/edit`)}
                  >
                    <FileEdit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
