---
description:
globs:
alwaysApply: false
---

# TypeScript Patterns & Type Safety

## Generated Types

- [packages/supabase-types/src/generated-types.ts](mdc:packages/supabase-types/src/generated-types.ts) - Auto-generated from Supabase schema
- [packages/supabase-types/src/index.ts](mdc:packages/supabase-types/src/index.ts) - Exported types for consumption
- Import pattern: `import { Database } from '@spritely/supabase-types'`

## Common Type Patterns

```typescript
// Database table row types
type PatientRow = Database["public"]["Tables"]["patients"]["Row"];
type AppointmentRow = Database["public"]["Tables"]["appointments"]["Row"];

// Extended types with computed fields
interface PatientWithStats extends PatientRow {
  age: number;
  full_name: string;
  status: "new" | "active" | "inactive" | "archived";
}
```

## Null Safety Patterns

- Always handle potential null values from database
- Use fallbacks: `patient.organization_id || ''`
- Null checks before rendering: `{user && <Component user={user} />}`
- Date handling: `new Date(activity.created_at || new Date().toISOString())`

## Type Assertion Guidelines

- Use `any` sparingly with ESLint disable comments
- Prefer type guards over assertions when possible
- Document complex type assertions with comments
- Example: `// eslint-disable-next-line @typescript-eslint/no-explicit-any`

## Common Type Issues & Solutions

1. **Boolean vs Boolean | null**: Use `!!value` to convert to boolean
2. **Promise conditions**: Use `'key' in object` instead of `object[key]` for Promise checks
3. **Ref assignments**: Use `useRef<Type | null>(null)` for mutable refs
4. **Supabase joins**: Complex joins may require `any` with proper comments

## Hook Return Types

```typescript
// Consistent hook return pattern
return {
  data: patients,
  isLoading,
  error,
  refetch,
};
```

## Error Handling Patterns

- Always type catch blocks: `catch (err: unknown)`
- Convert to Error type: `err instanceof Error ? err : new Error('message')`
- Provide meaningful error messages with context
