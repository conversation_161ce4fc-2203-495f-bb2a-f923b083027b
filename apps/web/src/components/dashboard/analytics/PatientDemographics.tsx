import {
    <PERSON>,
    CardContent,
    CardDescription,
    <PERSON><PERSON><PERSON>er,
    CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAnalytics } from "@/hooks/dashboard/useAnalytics";
import { useLocationAnalytics } from "@/hooks/dashboard/useLocationAnalytics";
import { useLocations } from "@/hooks/useLocations";
import { Cell, Pie, Pie<PERSON>hart, ResponsiveContainer, Tooltip } from "recharts";

export function PatientDemographics() {
  const { selectedLocation, shouldShowLocationSelector } = useLocations();
  
  // Conditionally use location-aware analytics when location is selected
  const shouldUseLocationAnalytics = shouldShowLocationSelector && 
    selectedLocation && 
    selectedLocation.id !== 'system-admin-all-locations';
  
  const orgAnalytics = useAnalytics();
  const locationAnalytics = useLocationAnalytics();
  
  // Choose which analytics to use based on location selection
  const { patientDemographics, isLoading, error } = shouldUseLocationAnalytics 
    ? locationAnalytics 
    : orgAnalytics;

  // Colors for the charts
  const COLORS = {
    age: ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'],
    gender: ['#8884D8', '#82CA9D', '#FFC658']
  };

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-medium">
            Patient Demographics
          </CardTitle>
          <CardDescription>Age and gender distribution</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <p className="text-sm text-red-500">
              Failed to load demographics data
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-medium">
            Patient Demographics
          </CardTitle>
          <CardDescription>Age and gender distribution</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-48 w-full" />
            </div>
            <div className="space-y-3">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-48 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Check if we have data to display
  const hasAgeData = patientDemographics.ageGroups.some(
    (item) => item.value > 0,
  );
  const hasGenderData = patientDemographics.genderDistribution.some(
    (item) => item.value > 0,
  );

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle className="text-lg font-medium">
          Patient Demographics
        </CardTitle>
        <CardDescription>Age and gender distribution</CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        {!hasAgeData && !hasGenderData ? (
          <div className="flex flex-col items-center justify-center h-60 gap-4">
            <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center">
              <span className="text-2xl">👥</span>
            </div>
            <p className="text-muted-foreground text-center">
              Not enough patient data to display demographics.
              <br />
              Add more patients to see age and gender distribution.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {hasAgeData && (
              <div>
                <h4 className="text-sm font-medium mb-3">Age Distribution</h4>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={patientDemographics.ageGroups}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {patientDemographics.ageGroups.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS.age[index % COLORS.age.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}

            {hasGenderData && (
              <div>
                <h4 className="text-sm font-medium mb-3">Gender Distribution</h4>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={patientDemographics.genderDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {patientDemographics.genderDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS.gender[index % COLORS.gender.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
