import { signOut } from "@/lib/auth/auth-service";
import {
    authStateReducer,
    getOrganization,
    getUser,
} from "@/lib/auth/auth-state-machine";
import { supabase } from "@/lib/supabase";
import { useOrganizationStore } from "@/stores/organization-store";
import type {
    AuthResponse,
    Session,
    UserResponse,
} from "@supabase/supabase-js";
import { useCallback, useEffect, useMemo, useReducer, useRef, useState } from "react";
import { AuthContext } from "./auth-context";
import { AuthContextType, Organization } from "./auth-context-types";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Use the state machine to manage auth state
  const [authState, dispatch] = useReducer(authStateReducer, {
    status: "initializing",
  });

  // Track session state
  const [session, setSession] = useState<Session | null>(null);

  // Track if we've completed initial auth check
  const [hasCompletedInitialCheck, setHasCompletedInitialCheck] = useState(false);

  // Track if component is mounted to avoid state updates after unmount
  const isMounted = useRef(true);

  // Track if we've already loaded organization data for the current user
  const loadedOrgForUser = useRef<string | null>(null);

  // Use the organization store
  const { currentOrg, loadUserOrganizations, clearCache } = useOrganizationStore();

  // Auth methods
  const handleSignUp = useCallback(
    async (
      email: string,
      password: string,
      metadata?: {
        first_name?: string;
        last_name?: string;
        organization_id?: string;
      },
    ): Promise<AuthResponse> => {
      return supabase.auth.signUp({
        email,
        password,
        options: { data: metadata },
      });
    },
    [],
  );

  const handleSignIn = useCallback(
    async (email: string, password: string): Promise<AuthResponse> => {
      return supabase.auth.signInWithPassword({
        email: email,
        password: password,
      });
    },
    [],
  );

  const handleResetPassword = useCallback(
    async (
      email: string,
    ): Promise<{ data: object | null; error: Error | null }> => {
      try {
        const { data, error } =
          await supabase.auth.resetPasswordForEmail(email);
        return { data, error: error as Error | null };
      } catch (error) {
        return { data: null, error: error as Error };
      }
    },
    [],
  );

  const handleUpdatePassword = useCallback(
    async (newPassword: string): Promise<UserResponse> => {
      return supabase.auth.updateUser({ password: newPassword });
    },
    [],
  );

  const handleSignOut = useCallback(async () => {
    clearCache();
    await signOut();
  }, [clearCache]);

  const handleSetOrganization = useCallback(
    (org: Organization | null) => {
      if (org) {
        console.info(`[AUTH] Setting organization: ${org.name} (${org.id})`);
        dispatch({ type: "LOAD_ORGANIZATION", organization: org });
      } else {
        console.info("[AUTH] Clearing organization (sign out)");
        dispatch({ type: "SIGN_OUT" });
      }
    },
    [],
  );

  // Load organization data when currentOrg changes from the store
  useEffect(() => {
    if (currentOrg && session?.user) {
      const currentOrgFromState = getOrganization(authState);
      if (!currentOrgFromState || currentOrgFromState.id !== currentOrg.id) {
        console.info(
          `[AUTH] Syncing organization from store: ${currentOrg.name} (${currentOrg.id})`,
        );
        dispatch({
          type: "LOAD_ORGANIZATION",
          organization: currentOrg,
        });
        // Organization data loaded successfully
      }
    }
  }, [currentOrg, session?.user, authState]);

  // Load organization data when user signs in
  useEffect(() => {
    if (session?.user && !loadedOrgForUser.current) {
      const userId = session.user.id;
      console.info(`[AUTH] Loading organization data for user: ${userId}`);
      loadedOrgForUser.current = userId;

      loadUserOrganizations(session.user).catch((error) => {
        console.error("[AUTH] Error loading organization data:", error);
        loadedOrgForUser.current = null;
        if (isMounted.current) {
          dispatch({
            type: "ERROR",
            error: new Error("Failed to load organization data"),
          });
        }
      });
    }
  }, [session?.user, loadUserOrganizations]);



  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Get initial session
        const { data: { session: initialSession } } = await supabase.auth.getSession();

        if (initialSession) {
          setSession(initialSession);
          dispatch({ type: "SIGN_IN", user: initialSession.user });
        } else {
          dispatch({ type: "SIGN_OUT" });
        }

        // Mark initial check as complete
        setHasCompletedInitialCheck(true);
      } catch (error) {
        console.error("[AUTH] Error initializing auth:", error);
        setHasCompletedInitialCheck(true);
        if (isMounted.current) {
          dispatch({ type: "ERROR", error: error as Error });
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!isMounted.current) return;

        console.debug(`[AUTH] Auth state changed: ${event}`);
        setSession(session);

        if (event === "SIGNED_IN" && session?.user) {
          dispatch({ type: "SIGN_IN", user: session.user });
          // Reset the loaded flag so we load org data for the new user
          loadedOrgForUser.current = null;
        } else if (event === "SIGNED_OUT") {
          dispatch({ type: "SIGN_OUT" });
          clearCache();
          loadedOrgForUser.current = null;
        } else if (event === "TOKEN_REFRESHED" && session?.user) {
          // Token refresh doesn't need to reload organization data
          dispatch({ type: "SIGN_IN", user: session.user });
        }
      },
    );

    return () => {
      isMounted.current = false;
      subscription.unsubscribe();
    };
  }, [clearCache]);

  // Calculate final organization and loading state
  const finalOrganization = getOrganization(authState) || currentOrg;

  // Only show loading screen on true initial app loads, not page refreshes
  // Check if we have cached session data to detect page refresh vs fresh load
  const hasCachedSession = useMemo(() => {
    try {
      const localToken = localStorage.getItem('supabase.auth.token');
      const sessionToken = sessionStorage.getItem('supabase.auth.token');
      const hasCached = !!localToken || !!sessionToken;
      console.log('[AUTH_PROVIDER] Cached session check:', {
        localToken: !!localToken,
        sessionToken: !!sessionToken,
        hasCached,
        currentUser: !!getUser(authState)
      });
      return hasCached;
    } catch (error) {
      console.log('[AUTH_PROVIDER] Error checking cached session:', error);
      return false;
    }
  }, [authState]);

  // Don't show loading if we have cached session data (page refresh) or if auth check is complete
  const finalIsLoading = !hasCompletedInitialCheck && !hasCachedSession;

  console.log('[AUTH_PROVIDER] Loading state calculation:', {
    hasCompletedInitialCheck,
    hasCachedSession,
    finalIsLoading,
    authStatus: authState.status,
    hasUser: !!getUser(authState)
  });

  // Create context value
  const contextValue: AuthContextType = {
    // State
    user: getUser(authState),
    session,
    organization: finalOrganization,
    isLoading: finalIsLoading,
    hasOrganization: !!finalOrganization,
    authState,

    // Actions
    signUp: handleSignUp,
    signIn: handleSignIn,
    signOut: handleSignOut,
    resetPassword: handleResetPassword,
    updatePassword: handleUpdatePassword,
    setOrganization: handleSetOrganization,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}
