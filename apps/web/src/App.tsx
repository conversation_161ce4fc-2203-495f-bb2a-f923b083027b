import { ErrorBoundary } from "@/components/ErrorBoundary";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { LoadingStateManager } from "@/components/loading/LoadingStateManager";
import { NavigationManager } from "@/components/navigation/NavigationManager";
import { AuthLayout, DashboardLayout, MainLayout } from "@/layouts";
import {
    DashboardPage,
    ForgotPasswordPage,
    LoginPage,
    NotFoundPage,
    OrganizationSettingsPage,
    OrganizationsManagePage,
    PatientDetailsPage,
    PatientEditPage,
    PatientsManagePage,
    RegisterPage,
    ResetPasswordPage,
    SettingsPage,
    SetupRouter,
} from "@/pages";
import { LandingPage } from "@/pages/home/<USER>";
import { ImprovedOrganizationsPage } from "@/pages/organizations/ImprovedOrganizationsPage";
import { Route, Routes } from "react-router-dom";
import "./App.css";

function App() {
  return (
    <ErrorBoundary>
      <Routes>
        {/* Landing page route */}
        <Route path="/" element={<LandingPage />} />

        {/* Auth routes */}
        <Route element={<AuthLayout />}>
          <Route
            path="/login"
            element={
              <NavigationManager>
                <LoginPage />
              </NavigationManager>
            }
          />
          <Route
            path="/register"
            element={
              <NavigationManager>
                <RegisterPage />
              </NavigationManager>
            }
          />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/reset-password" element={<ResetPasswordPage />} />
        </Route>

        {/* Protected routes - wrapped in LoadingStateManager */}
        <Route
          element={
            <LoadingStateManager>
              <DashboardLayout />
            </LoadingStateManager>
          }
        >
          <Route
            path="/dashboard/*"
            element={
              <ErrorBoundary>
                <NavigationManager>
                  <DashboardPage />
                </NavigationManager>
              </ErrorBoundary>
            }
          />

          {/* Organization Management */}
          <Route
            path="/organizations"
            element={
              <ErrorBoundary>
                <NavigationManager>
                  <ImprovedOrganizationsPage />
                </NavigationManager>
              </ErrorBoundary>
            }
          />

          <Route
            path="/organizations/manage"
            element={
              <ErrorBoundary>
                <NavigationManager>
                  <OrganizationsManagePage />
                </NavigationManager>
              </ErrorBoundary>
            }
          />

          <Route
            path="/organizations/:orgId/settings"
            element={
              <ErrorBoundary>
                <NavigationManager>
                  <OrganizationSettingsPage />
                </NavigationManager>
              </ErrorBoundary>
            }
          />

          <Route
            path="/settings"
            element={
              <ErrorBoundary>
                <NavigationManager>
                  <SettingsPage />
                </NavigationManager>
              </ErrorBoundary>
            }
          />

          {/* Patient Management */}
          <Route
            path="/patients"
            element={
              <ErrorBoundary>
                <NavigationManager>
                  <PatientsManagePage />
                </NavigationManager>
              </ErrorBoundary>
            }
          />

          <Route
            path="/patients/:patientId"
            element={
              <ErrorBoundary>
                <NavigationManager>
                  <PatientDetailsPage />
                </NavigationManager>
              </ErrorBoundary>
            }
          />

          <Route
            path="/patients/:patientId/edit"
            element={
              <ErrorBoundary>
                <NavigationManager>
                  <PatientEditPage />
                </NavigationManager>
              </ErrorBoundary>
            }
          />
        </Route>

        {/* Setup route - only for users without organizations */}
        <Route
          path="/setup/*"
          element={
            <LoadingStateManager>
              <ProtectedRoute requireOrganization={false}>
                <ErrorBoundary>
                  <SetupRouter />
                </ErrorBoundary>
              </ProtectedRoute>
            </LoadingStateManager>
          }
        />

        {/* Other main app routes */}
        <Route element={<MainLayout />}>
          {/* Catch unauthorized access attempts */}
          <Route
            path="/unauthorized"
            element={<div>You are not authorized to access this page</div>}
          />
        </Route>

        {/* Handle 404 errors */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </ErrorBoundary>
  );
}

export default App;


