# Task ID: 2
# Title: Implement Dashboard Container and Routing Logic
# Status: done
# Dependencies: 1
# Priority: high
# Description: Create a smart container component that handles routing and context management for different dashboard views.
# Details:
1. Create `DashboardContainer.tsx` in `/src/components/dashboard/`
2. Implement React Router v6 for navigation:
   ```typescript
   import { BrowserRouter, Routes, Route } from 'react-router-dom';
   ```
3. Create routes for system-level and organization-level dashboards
4. Implement context detection logic using React hooks:
   ```typescript
   const [currentContext, setCurrentContext] = useState<'system' | 'organization'>('system');
   ```
5. Add logic to switch between dashboard types based on selected organization
6. Implement breadcrumb navigation using @mantine/core
7. Create a context provider for sharing selected organization across components

# Test Strategy:
1. Unit test routing logic
2. Test context switching between 'All Organizations' and specific organizations
3. Verify breadcrumb navigation updates correctly
4. Test with mock data for different user roles (system admin, multi-tenant user, org admin)

# Subtasks:
## 1. Create DashboardContainer Component [done]
### Dependencies: None
### Description: Implement the main container component for the dashboard
### Details:
Create `DashboardContainer.tsx` in `/src/components/dashboard/`. Set up the basic structure of the component with necessary imports and a skeleton implementation.
<info added on 2025-05-25T04:02:20.976Z>
Create `DashboardContainer.tsx` in `/src/components/dashboard/` that will serve as a wrapper for the dashboard views. The component should:

1. Import and use the AuthProvider context to access organization state
2. Implement conditional rendering logic that:
   - Checks if organization.id === "system-admin-no-org" to identify system admins in "All Organizations" view
   - Renders a system-level dashboard for system admins with "All Organizations" view
   - Renders the existing organization-specific Dashboard component for regular users or system admins with a specific organization selected

3. Set up the basic structure with:
   ```tsx
   import React from 'react';
   import { useAuth } from '../../contexts/AuthContext';
   import Dashboard from './Dashboard';
   // Import system dashboard components

   const DashboardContainer: React.FC = () => {
     const { organization } = useAuth();
     
     const isSystemAdminView = organization?.id === "system-admin-no-org";
     
     return (
       <div className="dashboard-container">
         {isSystemAdminView ? (
           // Render system-level dashboard
           <SystemDashboard />
         ) : (
           // Render organization-specific dashboard
           <Dashboard />
         )}
       </div>
     );
   };

   export default DashboardContainer;
   ```

4. Ensure the component maintains the existing Dashboard.tsx functionality for organization-specific views while adding the new system-level dashboard capability.
</info added on 2025-05-25T04:02:20.976Z>

## 2. Implement React Router Setup [done]
### Dependencies: 2.1
### Description: Set up routing logic using React Router v6
### Details:
Import BrowserRouter, Routes, and Route from 'react-router-dom'. Define routes for system-level and organization-level dashboards within the DashboardContainer component.
<info added on 2025-05-25T04:04:28.156Z>
The routing logic has been implemented using conditional rendering within the DashboardContainer component rather than React Router routes. The component checks if the current view is for a system admin (organization?.id === "system-admin-no-org") and renders either SystemDashboard or Dashboard accordingly. This approach leverages the existing `/dashboard/*` route in App.tsx which renders DashboardPage, which in turn loads organization data and renders DashboardContainer. The organization context is managed by AuthProvider and accessed via the useAuth hook. No additional React Router setup is required as the routing is handled through this conditional rendering pattern.
</info added on 2025-05-25T04:04:28.156Z>

## 3. Develop Context Detection Logic [done]
### Dependencies: 2.2
### Description: Implement logic to detect and manage the current context (system or organization)
### Details:
Use React hooks to create a state for currentContext. Implement logic to switch between dashboard types based on the selected organization.
<info added on 2025-05-25T04:04:54.598Z>
The context detection logic has been successfully implemented in the DashboardContainer component. The implementation uses the useAuth hook to access the current organization and determines the dashboard context based on the organization ID. When the organization ID is "system-admin-no-org", the system displays the SystemDashboard component; otherwise, it shows the regular Dashboard component.

The context switching mechanism works automatically when users change organizations through the OrganizationSwitcher. When a new organization is selected, the AuthProvider updates the organization state, triggering a re-render of the DashboardContainer which then applies the context detection logic to show the appropriate dashboard view.

This implementation satisfies all requirements for context detection and automatic switching between dashboard types based on the selected organization.
</info added on 2025-05-25T04:04:54.598Z>

## 4. Create Organization Context Provider [done]
### Dependencies: 2.3
### Description: Implement a context provider for sharing selected organization data
### Details:
Create a new context using React.createContext(). Implement a provider component that wraps the DashboardContainer and provides the selected organization data to child components.
<info added on 2025-05-25T04:05:22.134Z>
## Organization Context Provider Implementation Status

After analysis, we've determined that a separate organization context provider is not needed as it already exists within the AuthProvider system:

### Existing Implementation
- **AuthContext**: Defined in `auth-context.tsx` using `React.createContext()`
- **AuthProvider**: Implemented in `AuthProvider.tsx` which provides organization data throughout the app
- **useAuth Hook**: Available in `hooks/useAuth.ts` for accessing organization context

### AuthContext Structure
The existing context already includes organization data:
```typescript
export type AuthContextType = {
  user: User | null;
  session: Session | null;
  organization: Organization | null;  // Organization context
  hasOrganization: boolean;
  setOrganization: (org: Organization | null) => void;  // Organization setter
  // ... other auth methods
};
```

### Current Usage in Dashboard
```tsx
const { organization } = useAuth();  // Accessing organization context
const isSystemAdminView = organization?.id === "system-admin-no-org";
```

### Conclusion
No additional context provider implementation is required as the organization context functionality is already fully implemented and functional within the existing authentication system.
</info added on 2025-05-25T04:05:22.134Z>

## 5. Add Breadcrumb Navigation [done]
### Dependencies: 2.2, 2.3
### Description: Implement breadcrumb navigation using @mantine/core
### Details:
Import necessary components from @mantine/core. Create a Breadcrumb component that updates based on the current route and context. Integrate this component into the DashboardContainer.
<info added on 2025-05-25T04:07:42.576Z>
**Breadcrumb Navigation Implementation Complete:**

**Created DashboardBreadcrumb Component:**
- Located at `/src/components/dashboard/DashboardBreadcrumb.tsx`
- Uses existing Radix UI breadcrumb components (not Mantine as originally specified)
- Implements context-aware breadcrumb navigation

**Features Implemented:**
1. **Context Detection**: Automatically detects system vs organization view
2. **Dynamic Breadcrumbs**: Shows different paths based on current context:
   - System Admin: Home → System Administration → [Current Page]
   - Organization: Home → [Organization Name] → [Current Page]
3. **Route Awareness**: Uses `useLocation` to determine current page
4. **Icon Integration**: Uses appropriate icons (Home, Settings, Building2)
5. **Link Navigation**: Breadcrumb items are clickable links

**Integration Complete:**
- ✅ Added to SystemDashboard component
- ✅ Added to Dashboard component
- ✅ TypeScript compilation successful
- ✅ Uses existing design system (Radix UI + Tailwind CSS)

**Breadcrumb Structure:**
```
System Admin View: Home → System Administration
Organization View: Home → [Organization Name]
```

The breadcrumb component automatically updates when users switch between organizations via the OrganizationSwitcher.
</info added on 2025-05-25T04:07:42.576Z>

