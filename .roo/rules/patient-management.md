---
description:
globs:
alwaysApply: false
---

# Patient Management System

## Core Patient Components

- [apps/web/src/pages/patients/PatientsPage.tsx](mdc:apps/web/src/pages/patients/PatientsPage.tsx) - Main patients list view
- [apps/web/src/pages/patients/PatientDetailPage.tsx](mdc:apps/web/src/pages/patients/PatientDetailPage.tsx) - Individual patient profile
- [apps/web/src/pages/patients/PatientEditPage.tsx](mdc:apps/web/src/pages/patients/PatientEditPage.tsx) - Patient editing form

## Patient Data Hooks

- [apps/web/src/hooks/patients/usePatients.ts](mdc:apps/web/src/hooks/patients/usePatients.ts) - Fetch patients list with filtering and pagination
- [apps/web/src/hooks/patients/usePatient.ts](mdc:apps/web/src/hooks/patients/usePatient.ts) - Fetch individual patient with appointments and medical records
- [apps/web/src/hooks/dashboard/usePatients.ts](mdc:apps/web/src/hooks/dashboard/usePatients.ts) - Dashboard-specific patient data

## Patient Components

- [apps/web/src/components/patients/PatientTable.tsx](mdc:apps/web/src/components/patients/PatientTable.tsx) - Main patients data table
- [apps/web/src/components/patients/PatientFilters.tsx](mdc:apps/web/src/components/patients/PatientFilters.tsx) - Filtering and search_files controls
- [apps/web/src/components/patients/PatientCard.tsx](mdc:apps/web/src/components/patients/PatientCard.tsx) - Patient card component

## Data Types

- [apps/web/src/types/patient.ts](mdc:apps/web/src/types/patient.ts) - Patient-related TypeScript types
- Uses Supabase generated types from `@spritely/supabase-types` package

## Key Features

- **Search & Filtering**: By name, status, organization
- **Pagination**: Server-side pagination with configurable page sizes
- **Organization Isolation**: RLS ensures users only see patients from their organization
- **System Admin Access**: Can view patients across all organizations
- **Patient Details**: Includes appointments, medical records, and computed fields (age, status)

## Database Integration

- Main table: `patients` with RLS policies
- Related tables: `appointments`, `medical_records`, `organizations`
- Computed fields: age (from date_of_birth), status (from recent appointments)
- Performance optimized: Single query for list view, detailed query for individual patient

## Recent Fixes

- Fixed SQL parsing errors in patient detail queries
- Simplified provider joins to avoid foreign key reference issues
- Added proper TypeScript typing with fallbacks for null values
- Optimized from 2+ queries down to 1 query for better performance
