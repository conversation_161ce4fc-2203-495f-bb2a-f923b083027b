---
description:
globs:
alwaysApply: false
---

# Authentication & Authorization System

## Auth Context

The main authentication logic is handled by [apps/web/src/contexts/AuthContext.tsx](mdc:apps/web/src/contexts/AuthContext.tsx), which provides:

- User authentication state
- Organization selection and switching
- Session management
- Auto-logout on session expiry

## Organization Management

- [apps/web/src/lib/auth/organization-service.ts](mdc:apps/web/src/lib/auth/organization-service.ts) - Core organization fetching logic
- [apps/web/src/lib/auth/organization-cache-exports.ts](mdc:apps/web/src/lib/auth/organization-cache-exports.ts) - Caching layer for organizations
- [apps/web/src/lib/auth/organization-types.ts](mdc:apps/web/src/lib/auth/organization-types.ts) - TypeScript types

## User Roles & Permissions

- [apps/web/src/hooks/useUserRoles.ts](mdc:apps/web/src/hooks/useUserRoles.ts) - Hook for fetching and caching user roles
- **System Admin**: Can access all organizations, manage system-wide settings
- **Org Admin**: Can manage their organization and users within it
- **Regular Users**: Limited to their assigned organization

## Organization Selectors

- [apps/web/src/components/organization-selector/OrganizationSelector.tsx](mdc:apps/web/src/components/organization-selector/OrganizationSelector.tsx) - Header organization selector
- [apps/web/src/components/organization/RoleBasedOrgSelector.tsx](mdc:apps/web/src/components/organization/RoleBasedOrgSelector.tsx) - Role-based organization selector

## Route Protection

- [apps/web/src/components/navigation/NavigationManager.tsx](mdc:apps/web/src/components/navigation/NavigationManager.tsx) - Handles route protection, redirects, and navigation logic
- Supports dynamic routes like `/patients/:id` and `/organizations/:id`
- Prevents infinite redirects with safety mechanisms

## Key Auth Patterns

- All API calls use Supabase RLS for data isolation
- Organization switching updates context and localStorage cache
- System admins have special "All Organizations" view (`system-admin-no-org`)
- Navigation stays on current page when switching organizations (no unwanted redirects)
