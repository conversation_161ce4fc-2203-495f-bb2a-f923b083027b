import { useCallback, useMemo } from "react";
import { useSearchParams } from "react-router-dom";

interface UsePaginationOptions {
  itemsPerPage?: number;
  totalCount: number;
  pageParamName?: string;
}

interface UsePaginationReturn {
  currentPage: number;
  totalPages: number;
  offset: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  goToPage: (page: number) => void;
  goToNextPage: () => void;
  goToPrevPage: () => void;
  goToFirstPage: () => void;
  goToLastPage: () => void;
}

export function usePagination({
  itemsPerPage = 50,
  totalCount,
  pageParamName = "page",
}: UsePaginationOptions): UsePaginationReturn {
  const [searchParams, setSearchParams] = useSearchParams();

  // Get current page from URL parameters
  const currentPage = useMemo(() => {
    const pageParam = searchParams.get(pageParamName);
    return Math.max(1, parseInt(pageParam || "1", 10));
  }, [searchParams, pageParamName]);

  // Calculate pagination values
  const totalPages = useMemo(() => {
    return Math.ceil(totalCount / itemsPerPage);
  }, [totalCount, itemsPerPage]);

  const offset = useMemo(() => {
    return (currentPage - 1) * itemsPerPage;
  }, [currentPage, itemsPerPage]);

  const hasNextPage = useMemo(() => {
    return currentPage < totalPages;
  }, [currentPage, totalPages]);

  const hasPrevPage = useMemo(() => {
    return currentPage > 1;
  }, [currentPage]);

  // Update URL with new page
  const updatePage = useCallback(
    (page: number) => {
      const newParams = new URLSearchParams(searchParams);
      if (page === 1) {
        newParams.delete(pageParamName); // Remove page param for page 1 (cleaner URLs)
      } else {
        newParams.set(pageParamName, page.toString());
      }
      setSearchParams(newParams);
    },
    [searchParams, setSearchParams, pageParamName]
  );

  // Navigation functions
  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        updatePage(page);
      }
    },
    [updatePage, totalPages]
  );

  const goToNextPage = useCallback(() => {
    if (hasNextPage) {
      updatePage(currentPage + 1);
    }
  }, [hasNextPage, updatePage, currentPage]);

  const goToPrevPage = useCallback(() => {
    if (hasPrevPage) {
      updatePage(currentPage - 1);
    }
  }, [hasPrevPage, updatePage, currentPage]);

  const goToFirstPage = useCallback(() => {
    updatePage(1);
  }, [updatePage]);

  const goToLastPage = useCallback(() => {
    updatePage(totalPages);
  }, [updatePage, totalPages]);

  return {
    currentPage,
    totalPages,
    offset,
    hasNextPage,
    hasPrevPage,
    goToPage,
    goToNextPage,
    goToPrevPage,
    goToFirstPage,
    goToLastPage,
  };
}
